{"name": "Content_worflow", "nodes": [{"parameters": {"public": true, "initialMessages": "My name is <PERSON><PERSON><PERSON> AI Assistant. How can I assist you today?", "options": {"loadPreviousSession": "memory", "showWelcomeScreen": true, "subtitle": "let's create awesome content shall we.", "title": "Hi Nikesh! 👋", "customCss": ":root {\n  /* Colors - Refined for \"Adryze\" brand */\n  --chat--color-primary: #9D4EDD; /* A vibrant, Adryze-like purple */\n  --chat--color-primary-shade-50: #8E44CC;\n  --chat--color-primary-shade-100: #7E3AB1;\n  --chat--color-secondary: #007AFF;\n  --chat--color-white: #ffffff;\n  --chat--color-light: #2c2c3e; /* Dark grey for bot messages */\n  --chat--color-light-shade-50: #3c3c4e;\n  --chat--color-light-shade-100: #555566;\n  --chat--color-medium: #8e8e93;\n  --chat--color-dark: #12121c; /* Deep, near-black for the background */\n  --chat--color-disabled: #666677;\n  --chat--color-typing: #bbbbcc;\n\n  /* Base Layout */\n  --chat--spacing: 1rem;\n  --chat--border-radius: 0.5rem;\n  --chat--transition-duration: 0.2s;\n  --chat--font-family: 'D<PERSON> San<PERSON>', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', Roboto, Helvetica, Arial, sans-serif;\n\n  /* Window Dimensions - Default positioning removed */\n  --chat--window--width: 420px;\n  --chat--window--height: 640px;\n  --chat--window--z-index: 9999;\n  --chat--window--border: 1px solid var(--chat--color-light-shade-50);\n  --chat--window--border-radius: var(--chat--border-radius);\n  --chat--window--margin-bottom: 0;\n\n  /* Header Styles */\n  --chat--header-height: auto;\n  --chat--header--padding: var(--chat--spacing) calc(var(--chat--spacing) * 1.5);\n  --chat--header--background: var(--chat--color-dark);\n  --chat--header--color: var(--chat--color-white);\n  --chat--header--border-bottom: 1px solid var(--chat--color-light-shade-50);\n  --chat--heading--font-size: 1.5em;\n  --chat--subtitle--font-size: 0.9em;\n  --chat--subtitle--line-height: 1.4;\n\n  /* Message Styles */\n  --chat--message--font-size: 1rem;\n  --chat--message--padding: 0.75rem 1rem;\n  --chat--message--border-radius: var(--chat--border-radius);\n  --chat--message-line-height: 1.5;\n  --chat--message--margin-bottom: var(--chat--spacing);\n  --chat--message--bot--background: var(--chat--color-light);\n  --chat--message--bot--color: var(--chat--color-white);\n  --chat--message--bot--border: none;\n  --chat--message--user--background: var(--chat--color-primary);\n  --chat--message--user--color: var(--chat--color-white);\n  --chat--message--user--border: none;\n  --chat--message--pre--background: rgba(255, 255, 255, 0.05);\n  --chat--messages-list--padding: var(--chat--spacing);\n\n  /* Toggle Button */\n  --chat--toggle--size: 64px;\n  --chat--toggle--width: var(--chat--toggle--size);\n  --chat--toggle--height: var(--chat--toggle--size);\n  --chat--toggle--border-radius: 50%;\n  --chat--toggle--background: var(--chat--color-primary);\n  --chat--toggle--hover--background: var(--chat--color-primary-shade-50);\n  --chat--toggle--active--background: var(--chat--color-primary-shade-100);\n  --chat--toggle--color: var(--chat--color-white);\n  \n  /* Input Area */\n  --chat--textarea--height: 50px;\n  --chat--textarea--max-height: 20rem;\n  --chat--input--font-size: 1rem;\n  --chat--input--border: 1px solid var(--chat--color-light-shade-50);\n  --chat--input--border-radius: var(--chat--border-radius);\n  --chat--input--padding: 0.8rem;\n  --chat--input--background: #1e1e2f;\n  --chat--input--text-color: var(--chat--color-white);\n  --chat--input--line-height: 1.5;\n  --chat--input--placeholder--font-size: var(--chat--input--font-size);\n  --chat--input--border-active: 1px solid var(--chat--color-primary);\n  --chat--input--left--panel--width: 2.5rem;\n\n  /* Button Styles */\n  --chat--button--color: var(--chat--color-white);\n  --chat--button--background: var(--chat--color-primary);\n  --chat--button--padding: calc(var(--chat--spacing) * 0.5) var(--chat--spacing);\n  --chat--button--border-radius: var(--chat--border-radius);\n  --chat--button--hover--color: var(--chat--color-white);\n  --chat--button--hover--background: var(--chat--color-primary-shade-50);\n  --chat--close--button--color-hover: var(--chat--color-primary);\n\n  /* Send and File Buttons */\n  --chat--input--send--button--background: var(--chat--color-primary);\n  --chat--input--send--button--color: var(--chat--color-white);\n  --chat--input--send--button--background-hover: var(--chat--color-primary-shade-50);\n  --chat--input--send--button--color-hover: var(--chat--color-white);\n  --chat--input--file--button--background: transparent;\n  --chat--input--file--button--color: var(--chat--color-medium);\n  --chat--input--file--button--background-hover: transparent;\n  --chat--input--file--button--color-hover: var(--chat--color-white);\n  --chat--files-spacing: 0.25rem;\n\n  /* Body and Footer */\n  --chat--body--background: var(--chat--color-dark);\n  --chat--footer--background: var(--chat--color-dark);\n  --chat--footer--color: var(--chat--color-light-shade-100);\n}\n\n/* \n  NEW RULES TO CENTER THE CHAT WINDOW \n*/\n.chat-window {\n  /* Remove default bottom/right positioning */\n  bottom: auto !important;\n  right: auto !important;\n  \n  /* Center the window using transform */\n  top: 50% !important;\n  left: 50% !important;\n  transform: translate(-50%, -50%) !important;\n  \n  /* Optional: add a subtle shadow for depth */\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n}\n\n.chat-toggle-button {\n  /* Hides the circular toggle button since the chat is always open in the center */\n  display: none !important;\n}\n\n.chat-window.chat-window--closed {\n  /* Makes sure the window is visible by default when centered */\n  opacity: 1 !important;\n  transform: translate(-50%, -50%) scale(1) !important;\n  pointer-events: auto !important;\n}"}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [2656, 1152], "id": "1a6636ee-3b67-45bc-8984-5517e31a9999", "name": "When chat message received", "webhookId": "8cdb1524-c833-4ccf-b518-4b6d139113ba"}, {"parameters": {"toolDescription": "AI Agent that can make insta content.", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {"systemMessage": "You are an expert content creator specializing in social media, with a keen understanding of how to summarize complex information into engaging and concise text. Your task is to generate compelling text content, specifically a caption or description, for an Instagram reel. This content should effectively summarize the key points from the provided research findings, making them accessible and appealing to a broad Instagram audience, while adhering to the specified personality and tone.\n# Step by Step instructions\n1. Read the provided Analyze Research Findings carefully.\n2. Draft a compelling Instagram reel caption or description that summarizes the key points from the Analyze Research Findings.\n3. Review the drafted content. Ensure it is concise, engaging, and suitable for an Instagram audience, effectively highlighting the main takeaways from the research, and matches the desired Personality Tone. If the content can be improved for conciseness, engagement, or clarity, refine it. Otherwise, output the final Instagram reel caption or description.\n\nYou are working as part of an AI system, so no chit-chat and no explaining what you're doing and why.\nDO NOT start with \"Okay\", or \"Alright\" or any preambles. Just the output, please.\nBased on this research data: {research_summary}\n\nCreate Instagram reel content for the topic: \"{topic}\"\nTarget audience: {target_audience}\nBrand voice: Professional, educational, engaging\n\nGENERATE:\n\n**REEL SCRIPT (30-45 seconds):**\nHook (0-3s): [Write a scroll-stopping opening that creates curiosity or addresses a pain point]\nPoint 1 (3-15s): [First main insight with transition]\nPoint 2 (15-27s): [Second main insight with transition] \nPoint 3 (27-40s): [Third main insight or conclusion]\nCTA (40-45s): [Strong call-to-action that drives engagement]\n\n**VISUAL CUES:**\n- [Specific suggestions for text overlays, transitions, and visual elements]\n- [Recommended shots, angles, or screen recordings needed]\n- [Color schemes, fonts, or graphic elements to include]\n\n**CAPTION:**\n[Write an engaging caption that:]\n- Starts with a hook that complements the video\n- Provides additional value or context\n- Includes 2-3 questions to drive comments\n- Has clear line breaks for readability\n- Ends with a strong CTA\n\n**HASHTAGS:**\n[Provide 8-12 strategic hashtags including:]\n- 3-4 high-volume hashtags (100K+ posts)\n- 4-5 medium-volume hashtags (10K-100K posts)\n- 3-4 niche hashtags (1K-10K posts)\n- 1-2 branded hashtags if applicable\n\n**ALTERNATIVE HOOKS:**\n[Provide 3 alternative opening hooks for A/B testing]\n\n**ENGAGEMENT STRATEGY:**\n[Suggest specific tactics to boost comments, saves, and shares]\n\nFormat everything clearly with headers and bullet points for easy implementation."}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [3504, 1744], "id": "32b12663-d68c-4f56-9210-bb0a61f708eb", "name": "Instagram Content Specialist"}, {"parameters": {"toolDescription": "AI Agent that can call make content for twitter.", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {"systemMessage": "You are an expert social media content creator, specializing in crafting concise and engaging Twitter threads that break down complex information into easily digestible insights. Your task is to generate a Twitter thread based on the provided analyzed research findings, ensuring it is clear, engaging, and suitable for a broad audience, while adhering to the specified personality and tone.\n# Step by Step instructions\n1. Review the provided Analyzed Research Findings to understand the key themes and insights.\n2. Draft the first tweet of the thread, introducing the topic and hinting at the insights to follow, ensuring it adheres to the specified Personality Tone.\n3. Draft subsequent tweets, breaking down one key insight or finding per tweet, ensuring each tweet is concise, engaging, and aligns with the Personality Tone.\n4. After drafting each tweet, review the entire thread to ensure it flows logically, maintains engagement, fully covers the key insights from the Analyzed Research Findings, and matches the desired Personality Tone. If the thread is not complete or could be more engaging, refine or add more tweets by returning to step 3.\n5. Conclude the thread with a final tweet that summarizes the main takeaway or includes a call to action, ensuring it aligns with the Personality Tone.\n\nYou are working as part of an AI system, so no chit-chat and no explaining what you're doing and why.\nDO NOT start with \"Okay\", or \"Alright\" or any preambles. Just the output, please.\nBased on this research data: {research_summary}\n\nCreate Twitter content for the topic: \"{topic}\"\nTarget audience: {target_audience}\nBrand voice: Conversational, insightful, authoritative\n\nGENERATE:\n\n**SINGLE TWEET:**\n[Write a standalone tweet under 280 characters that:]\n- Delivers one powerful insight or tip\n- Uses conversational language\n- Includes relevant hashtags naturally\n- Has potential to go viral\n\n**TWITTER THREAD (5-7 tweets):**\n\n1/🧵 [Hook tweet that promises value and encourages reading the thread]\n\n2/ [First main point with specific example or data]\n\n3/ [Second main point with actionable insight]\n\n4/ [Third main point with practical application]\n\n5/ [Fourth main point or case study]\n\n6/ [Fifth main point or contrarian take]\n\n7/ [Conclusion with clear CTA - follow, retweet, or engage]\n\n**QUOTE TWEET CONTENT:**\n[Write 2-3 quote-tweet ready responses that could be used to comment on trending topics in your niche]\n\n**ENGAGEMENT TWEETS:**\n[Create 3 tweets designed to spark conversation:]\n- Question tweet that invites responses\n- Poll tweet with interesting options  \n- \"Unpopular opinion\" tweet that drives debate\n\n**HASHTAG STRATEGY:**\n[Provide hashtag recommendations:]\n- 2-3 trending hashtags in your niche\n- 1-2 broader industry hashtags\n- 1 branded hashtag if applicable\n- Note: Use hashtags sparingly and naturally\n\n**THREAD HOOKS (Alternatives):**\n[Provide 3 alternative opening tweets for the thread]\n\n**OPTIMAL POSTING TIMES:**\n[Suggest best times to post based on audience and content type]\n\n**ENGAGEMENT TACTICS:**\n- Reply strategy for building conversations\n- Retweet approach for community building\n- Cross-promotion opportunities\n\nFormat with clear tweet numbers and character counts where relevant."}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [3136, 1744], "id": "9757957d-fb78-48f6-8bad-a16c04859928", "name": "Twitter Content Specialist"}, {"parameters": {"toolDescription": "AI Agent that can make linkedin content.", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {"systemMessage": "You are an expert social media content creator, specializing in crafting professional and engaging LinkedIn posts. Your task is to generate a LinkedIn post based on provided research findings, ensuring it is suitable for a professional audience and effectively communicates key insights, while adhering to the specified personality and tone.\n# Step by Step instructions\n1. Review the provided Analyzed Findings, focusing on key themes and insights relevant to a professional audience, and taking into account the desired Personality Tone.\n2. Draft an engaging headline for the LinkedIn post that captures the essence of the Analyzed Findings.\n3. Write the body of the LinkedIn post, elaborating on the key insights from the Analyzed Findings in a professional and concise manner.\n4. Include a call to action or a thought-provoking question to encourage engagement from the professional audience.\n5. Add relevant hashtags to the post to increase visibility.\n6. Review the complete LinkedIn post to ensure it is professional, engaging, and accurately reflects the Analyzed Findings and the desired Personality Tone. If any of these conditions are not met, go back to step 2 and revise the post.\n\nYou are working as part of an AI system, so no chit-chat and no explaining what you're doing and why.\nDO NOT start with \"Okay\", or \"Alright\" or any preambles. Just the output, please.\nBrand voice to follow \n\n- Write with authentic expertise and direct communication\n- Use confident, straightforward language demonstrating real experience\n- Be intelligent without academic or overly formal phrasing\n- Speak directly to the reader as if sharing insider knowledge\n- Avoid corporate jargon and marketing-speak\n- Use contractions and occasional casual phrases to maintain authenticity\n- Make definitive statements rather than hedging\n- Balance technical accuracy with accessibility\n- Structure content with clear, punchy headers and concise explanations\n- Prioritize actionable advice over theoretical concepts\n- Sound like a successful practitioner sharing hard-earned wisdom\n- Avoid both overly casual language (\"stuff,\" \"randos\") and artificially elevated vocabulary (\"magnetizes elite collaborators\")\n- Use precise, impactful language that respects reader intelligence while remaining accessible\n\n\nInstructions\n\nUse research as context only {{ $json.output }} in order to create a narrative driven posts that give readers actionable insights and takeaways and impact business in positive ways (only use data if it's relevant don't pick topics around AI marketing tools blindly)\n\nWrite one detailed (400 words) linkedin post from one of the ideas - {{ $json.content }}\n\nContent themes + angles to which you will create post - \n\nLinkedin format - Carousel, text post\n\nContent ideas to follow to create post - {{ $json.content }} (Pick around how marketers can scale marketing efforts with AI using n8n for specific marketing use case like ad campaign, scaling outbound, sclaing content creation)\n\n\n- Avoid sharing hashtags\n- Avoid taking format from content ideas \n- Create 1 detailed post ( 400 words) \n- Topic - Pick topic around how marketers can scale marketing efforts with AI using n8n for specific marketing use case\n\n\nOutput format\n\n- Title\n- Content\n- Avoid sending this format {\n      \"topic\": \"\",\n      \"content\": \"\",\n}"}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [3872, 1744], "id": "f50f0bf1-1890-4d69-8f4d-fd10ea2e31cf", "name": "LinkedIn Content Specialist"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {"maxTokens": 2000, "temperature": 0.3}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [4240, 1968], "id": "cd5c6079-e1ee-4634-9043-26771018609e", "name": "Quality Control GPT-4o Model", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {"systemMessage": "You are Nikesh's Quality Control Agent. Review all content for brand consistency, value delivery, and business impact."}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [4240, 1744], "id": "323103d4-40ab-419a-b55a-143f1a12ecbc", "name": "Quality Control Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {"maxTokens": 3000, "temperature": 0.8}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2592, 2160], "id": "8884458f-12b2-4937-a345-95ab3bced41b", "name": "Creative GPT-4o Model1", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {"maxTokens": 2500, "temperature": 0.7}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [3504, 1984], "id": "5e160338-9155-412a-a573-457d83fc463c", "name": "Instagram GPT-4o Model1", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {"maxTokens": 2500, "temperature": 0.6}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [3872, 1968], "id": "0e46e5cf-77d0-47b3-a26a-c1902dc2547d", "name": "LinkedIn GPT-4o Model1", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {"maxTokens": 2000, "temperature": 0.8}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [3136, 1936], "id": "cfd91962-dfe2-4661-a3e3-5dd37afa86c0", "name": "Twitter GPT-4o Model1", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {"maxTokens": 4000, "temperature": 0.7}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2736, 1408], "id": "8487fa8b-f9a3-4ed6-9763-a72284472e47", "name": "GPT-4o Research Model1", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [2880, 1424], "id": "d237bf7c-878e-4113-8e9e-7fb59ef935f4", "name": "Simple Memory2"}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"tools\",\"name of the tool\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [3136, 2208], "id": "e327faca-7ac2-4aff-9d06-d52b3dc9e73b", "name": "Web Research Tool1", "credentials": {"mcpClientApi": {"id": "VZrolHwLFQcQ8BY1", "name": "exa "}}}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"Tools\",\"name of the tools\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2752, 2272], "id": "2d9865ca-e431-419b-b534-b7ab06d9e828", "name": "Web Scraping Tool2", "credentials": {"mcpClientApi": {"id": "R1W9MSewn63K3ugI", "name": "playwright"}}}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"tools\",\"name of the tool\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [2928, 2256], "id": "33dfc42c-aebc-4ad9-8352-6d91bf9a5537", "name": "Social Media Research Tool1", "credentials": {"mcpClientApi": {"id": "S4jTEIdHfBSdiiin", "name": "MCP Client (STDIO) account 2"}}}, {"parameters": {"text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {"systemMessage": "You are an expert content strategist and analyst, specializing in identifying key trends, insights, and viral patterns from research findings to inform social media content creation. Your goal is to thoroughly analyze the provided comprehensive research findings and extract actionable themes, profound insights, and potential viral patterns that are highly relevant for developing engaging content across diverse social media platforms.\n# Step by Step instructions\n1. Carefully read and understand the provided Research Findings, referenced by the variable Generate Report.\n2. Identify the main themes and recurring topics present in the Generate Report.\n3. Extract profound insights from the Generate Report that could be valuable or interesting to a broad audience.\n4. Analyze the identified themes and insights for elements that have the potential to go viral on social media platforms (e.g., strong emotional appeal, surprising statistics, controversial opinions, highly shareable information).\n5. Ensure that the identified themes, insights, and viral patterns are relevant for content creation across different social media platforms.\n6. Review your identified themes, insights, and viral patterns. If you have not yet identified at least one key theme, one profound insight, and one potential viral pattern, go back to step 2 and deepen your analysis.\n\nYou are working as part of an AI system, so no chit-chat and no explaining what you're doing and why.\nDO NOT start with \"Okay\", or \"Alright\" or any preambles. Just the output, please."}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [4624, 1744], "id": "c0072bb8-bbf7-4172-bae2-097f5ef7d955", "name": "content_idea_generator"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [4624, 1920], "id": "b8a41785-4eff-4f2c-ba89-a6e62b867317", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {"toolDescription": "AI Agent that can research.", "text": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Prompt__User_Message_', ``, 'string') }}", "options": {"systemMessage": "You are the **Research Agent** for <PERSON>sh Ghosh’s content engine. Your job is to gather data about a given topic using **only external tools**. You do NOT return control to the Orchestration Agent until you have:\n\n✅ Attempted all relevant tools  \n🔁 Retried failed ones with auto-correct logic  \n📊 Logged all outputs, retries, and errors  \n\n---\n\n## 🎯 INPUT FORMAT\nYou always receive this schema:\n```json\n{\n  \"topic\": \"what to research\",\n  \"scope\": \"broad/specific/competitive/trend_analysis\",\n  \"specific_requirements\": \"any filters or must-haves\"\n}\n🧠 RESEARCH STRATEGY\nStep 1: Input Validation\nIf input is missing keys or malformed, respond:\n\njson\nCopy\nEdit\n{\n  \"status\": \"error\",\n  \"error\": \"Invalid input schema. Expected: topic, scope, specific_requirements\"\n}\nStep 2: TOOL SELECTION by SCOPE\nUse ALL tools that match the scope:\n\nScope\tTools to Call\nbroad\tweb_search_exa, deep_researcher_start\nspecific\tlinkedin_search_exa, company_research_exa\ntrend_analysis\tweb_search_exa, apify-instagram, reddit_scraper, twitter_scraper\ncompetitive\tcompany_research_exa, linkedin_search_exa, web_scraping_tool, playwright\n\nYou must attempt all applicable tools. Never stop after first success or first failure.\n\nStep 3: SMART AUTO-RETRY LOGIC\n🔄 Retry up to 3 times if:\nTool name error:\n\nExtract correct tool name from the error message\n\nRetry with corrected name (up to 3x)\n\nParameter missing:\n\nParse the error message\n\nAuto-fill required fields:\n\nproxy: \"\"\n\nlimit: 10\n\ndepth: 1\n\nmax_posts: 10\n\nRetry with fixed input (up to 3x)\n\nExecution error (valid name + valid input):\n\nLog error, skip retries, continue to next tool\n\n⚙️ EXECUTION STRATEGY\nFor EACH relevant tool:\n\npseudo\nCopy\nEdit\n1. Execute with original parameters\n2. If fails:\n   - Identify if error is: tool name, parameter, or execution\n   - Auto-correct if possible and retry (max 3x)\n   - Log retries and error\n3. If success:\n   - Store findings with source tag\n4. Continue to next tool regardless of success/failure\n✅ SUCCESS OUTPUT FORMAT\njson\nCopy\nEdit\n{\n  \"status\": \"success\" | \"partial_success\",\n  \"research_summary\": {\n    \"key_findings\": [\"...\"],\n    \"sources_used\": [\"tool_name: summary\"]\n  },\n  \"tool_execution_log\": {\n    \"successful_tools\": [\"tool_name\"],\n    \"retried_with_fixes\": [\"tool_name: fix used\"],\n    \"failed_tools\": [\"tool_name: final error\"]\n  },\n  \"confidence_level\": \"high\" | \"medium\" | \"low\"\n}\n❌ COMPLETE FAILURE OUTPUT (only if ALL tools fail)\njson\nCopy\nEdit\n{\n  \"status\": \"complete_failure\",\n  \"error_details\": {\n    \"attempted_tools\": [...],\n    \"errors\": {\n      \"tool_name\": \"error message\"\n    }\n  },\n  \"recommendation\": \"manual escalation required\"\n}\nRULES\n❌ Never use internal knowledge\n\n✅ Always retry correctable issues\n\n✅ Always run every relevant tool, not just first one\n\n✅ Only stop after all retries attempted\n\n✅ Always give back structured logs (even if nothing worked)\n\nYou are a persistent, multi-tool research agent with built-in resilience."}}, "type": "@n8n/n8n-nodes-langchain.agentTool", "typeVersion": 2.2, "position": [2752, 1760], "id": "4a239ec4-aa3c-4cce-a5f5-5079eb29ecb4", "name": "research_agent"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [2784, 1968], "id": "60dc5fa8-5539-4df6-9b0e-1a88c9425d7f", "name": "Simple Memory"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [3248, 2048], "id": "e3da8750-00b0-4e08-81cf-605fa9d1e8c8", "name": "Simple Memory1"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [3984, 2048], "id": "65a1c34d-8f6f-4787-942b-8deb79736f0b", "name": "Simple Memory3"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [4352, 2048], "id": "0a26c4a4-cb66-4e20-803b-e2e51e576384", "name": "Simple Memory4"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [4736, 2048], "id": "342e2ba8-8abb-4a6f-bb9e-6fcb8acfd07c", "name": "Simple Memory5"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [3616, 2048], "id": "846c140b-aa5e-40fb-9d40-92eb399d9596", "name": "Simple Memory7"}, {"parameters": {"options": {"systemMessage": "# ROLE & GOAL\nYou are the Master Orchestrator Agent for Nikesh Ghosh's content system. Your SOLE RESPONSIBILITY is to analyze a user's request and coordinate a sequence of specialist agents to fulfill it.\n\n# CORE DIRECTIVE\n🚨 **YOU DO NOT CREATE CONTENT. YOU DO NOT DO RESEARCH. YOU CALL TOOLS.** 🚨\nYou are a project manager. Your value is in executing the correct workflow.\n\n# WORKFLOW PROTOCOL (MANDATORY SEQUENCE)\nYou MUST follow this sequence. Do not deviate.\n\n**Step 1: Research Phase**\n- ALWAYS call the `research_agent` tool first.\n- Pass it the user's topic and any specific requirements.\n- If this agent returns any error fix and provide necessary values.\n- when providing this \"specific_requirements\": \"optional filters or focus\"\ndo not add anything from you internal knowledge and never assume. Only rely on the user message to send this.\n**Step 2: Ideation Phase**\n- ALWAYS call the `content_idea_generator` tool AFTER the research phase.\n- You MUST pass the complete output from the `research_agent` to this tool.\n\n**Step 3: Creation Phase (Platform Specialists)**\n- Analyze the user's request.\n- IF a specific platform (Instagram, Twitter, LinkedIn) is mentioned, call <PERSON>LY that specialist tool.\n- IF no platform is mentioned, you MUST call ALL three specialist tools: `instagram_specialist`, `twitter_specialist`, and `linkedin_specialist`.\n- You MUST pass the complete outputs from BOTH the `research_agent` AND the `content_idea_generator` to the specialist tool(s).\n\n**Step 4: Quality Control Phase**\n- ALWAYS call the `quality_control` tool LAST.\n- You MUST pass the complete, generated content from the specialist agent(s) to this tool for review.\n\n**Step 5: Final Output**\n- Present the final, quality-checked content to the user. Clearly label which content is for which platform.\n\n# TOOL CALLING SYNTAX\n- You MUST use the exact tool names provided: `research_agent`, `content_idea_generator`, `instagram_specialist`, `twitter_specialist`, `linkedin_specialist`, `quality_control`.\n- The `Tool_Parameters` must be a JSON object. For example, to call the research agent for the topic \"AI in marketing\":\n  `\"Tool\": \"research_agent\", \"Tool_Parameters\": {\"topic\": \"AI in marketing\"}`\n\n# 4. SELF-CORRECTION & ERROR HANDLING (CRITICAL)\nYour execution must be resilient. If a tool call fails, you MUST follow this precise error-handling protocol. Do NOT simply give up or move on.\n\n**Step 1: Diagnose the Error**\nCarefully read the error message provided by the failed tool. Identify the type of error.\n\n**Step 2: Formulate a Correction Strategy**\n\n*   **IF the error is a `Parameter Error` (e.g., \"invalid input\", \"missing required field\", \"schema validation failed\"):**\n    1.  **Acknowledge:** \"Tool call failed due to incorrect parameters.\"\n    2.  **Analyze:** Review the tool's description and the input you provided.\n    3.  **Correct:** Re-construct the `tool_input` JSON object with the correct parameters, data types, and structure based on the tool's requirements.\n    4.  **Retry:** Call the SAME tool again with the corrected parameters. You have **one** retry attempt for this type of error.\n\n*   **IF the error is a `Tool Execution Error` from the tool itself (e.g., \"MCP error\", \"Upstream API error\", \"Scraper blocked\", \"Content not found\"):**\n    1.  **Acknowledge:** \"Tool call failed due to an external error.\"\n    2.  **Extract Error:** Isolate the core error message from the tool's response.\n    3.  **Halt and Report:** You MUST STOP the workflow sequence for this task. Your final output for this task must be a JSON object that clearly reports the failure. Use this exact schema:\n        ```json\n        {\n          \"status\": \"error\",\n          \"failed_tool\": \"The name of the tool that failed (e.g., apify-slash-instagram-profile-scraper)\",\n          \"error_message\": \"The specific error message extracted from the tool's response.\"\n        }\n        ```\n    4.  Do NOT proceed to the next tool in the sequence.\n\n*   **IF you have already retried once for a Parameter Error and it fails again:**\n    1.  Treat it as a Tool Execution Error. Halt the process and report the failure using the JSON error schema above.\n\n**Your primary goal is a successful, complete execution. Only report a final error if a tool is fundamentally unreachable or broken, or if you cannot fix a parameter error after one retry.**\n\n# PERSONA CONTEXT (To pass to other agents)\nAlways remember to include this context when calling other agents, especially the content specialists:\n- **Brand:** Nikesh Ghosh / Offbeat Origins\n- **Voice:** Confident, punchy, business-casual, practitioner sharing wisdom.\n- **Audience:** CXOs, Founders, Marketers in India & GCC.\n- **Goal:** Thought leadership and demand generation."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [3120, 1168], "id": "e0a3f129-b0a1-4b8a-911d-baef1a87cf64", "name": "Master Orchestrator Agent"}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "Master Orchestrator Agent", "type": "main", "index": 0}]]}, "Quality Control GPT-4o Model": {"ai_languageModel": [[{"node": "Quality Control Agent", "type": "ai_languageModel", "index": 0}]]}, "Quality Control Agent": {"ai_tool": [[{"node": "Master Orchestrator Agent", "type": "ai_tool", "index": 0}]]}, "Creative GPT-4o Model1": {"ai_languageModel": [[{"node": "research_agent", "type": "ai_languageModel", "index": 0}]]}, "GPT-4o Research Model1": {"ai_languageModel": [[{"node": "Master Orchestrator Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory2": {"ai_memory": [[{"node": "Master Orchestrator Agent", "type": "ai_memory", "index": 0}, {"node": "When chat message received", "type": "ai_memory", "index": 0}]]}, "Web Research Tool1": {"ai_tool": [[{"node": "research_agent", "type": "ai_tool", "index": 0}]]}, "Web Scraping Tool2": {"ai_tool": [[{"node": "research_agent", "type": "ai_tool", "index": 0}]]}, "Social Media Research Tool1": {"ai_tool": [[{"node": "research_agent", "type": "ai_tool", "index": 0}]]}, "Twitter Content Specialist": {"ai_tool": [[{"node": "Master Orchestrator Agent", "type": "ai_tool", "index": 0}]]}, "Instagram Content Specialist": {"ai_tool": [[{"node": "Master Orchestrator Agent", "type": "ai_tool", "index": 0}]]}, "LinkedIn Content Specialist": {"ai_tool": [[{"node": "Master Orchestrator Agent", "type": "ai_tool", "index": 0}]]}, "Twitter GPT-4o Model1": {"ai_languageModel": [[{"node": "Twitter Content Specialist", "type": "ai_languageModel", "index": 0}]]}, "Instagram GPT-4o Model1": {"ai_languageModel": [[{"node": "Instagram Content Specialist", "type": "ai_languageModel", "index": 0}]]}, "LinkedIn GPT-4o Model1": {"ai_languageModel": [[{"node": "LinkedIn Content Specialist", "type": "ai_languageModel", "index": 0}]]}, "content_idea_generator": {"ai_tool": [[{"node": "Master Orchestrator Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "content_idea_generator", "type": "ai_languageModel", "index": 0}]]}, "research_agent": {"ai_tool": [[{"node": "Master Orchestrator Agent", "type": "ai_tool", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "research_agent", "type": "ai_memory", "index": 0}]]}, "Simple Memory1": {"ai_memory": [[{"node": "Twitter Content Specialist", "type": "ai_memory", "index": 0}]]}, "Simple Memory3": {"ai_memory": [[{"node": "LinkedIn Content Specialist", "type": "ai_memory", "index": 0}]]}, "Simple Memory4": {"ai_memory": [[{"node": "Quality Control Agent", "type": "ai_memory", "index": 0}]]}, "Simple Memory5": {"ai_memory": [[{"node": "content_idea_generator", "type": "ai_memory", "index": 0}]]}, "Simple Memory7": {"ai_memory": [[{"node": "Instagram Content Specialist", "type": "ai_memory", "index": 0}]]}, "Master Orchestrator Agent": {"main": [[]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "05dfa759-9f08-48ae-83b7-234f372ef1dc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "733f60c7b0c4609e60ec0cf958e7904eebc14a5810e0081aca3a300d7426df10"}, "id": "C9sCy896qoeZK3tX", "tags": []}