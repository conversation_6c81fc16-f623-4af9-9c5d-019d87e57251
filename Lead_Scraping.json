{"name": "Lead Scraping", "nodes": [{"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "b8ff2673-0cbc-4ec7-adbf-7b7cbedf8c63", "name": "When Executed by Another Workflow"}, {"parameters": {"url": "https://api.apify.com/v2/acts/code_crafter~apollo-io-scraper/run-sync-get-dataset-items?token=**********************************************", "sendQuery": true, "queryParameters": {"parameters": [{"name": "token", "value": "<API-KEY>"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"getPersonalEmails\": true,\n    \"getWorkEmails\": true,\n    \"totalRecords\": 500,\n    \"url\": \"{{ $json.finalURL }}\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 0], "id": "e4846ab7-4f2c-40e8-b815-d7f8b51900be", "name": "<PERSON>raper"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "1NFxfydqH0R1B_0d5pj0wM6YZ_OjC-FDGse6eL1MPujg", "mode": "list", "cachedResultName": "Lead Agent", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NFxfydqH0R1B_0d5pj0wM6YZ_OjC-FDGse6eL1MPujg/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": "gid=0", "mode": "list", "cachedResultName": "Sheet1", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/1NFxfydqH0R1B_0d5pj0wM6YZ_OjC-FDGse6eL1MPujg/edit#gid=0"}, "columns": {"mappingMode": "defineBelow", "value": {"Full Name ": "={{ $json.fullName }}", "Email Address": "={{ $json.emailAddress }}", "Phone Number": "={{ $json.Number }}", "Country ": "={{ $json.country }}", "Location": "={{ $json.location }}", "Industry": "={{ $json.businessIndustry }}", "Company Name": "={{ $json.companyName }}", "Job Title": "={{ $json.jobTitle }}", "Seniority": "={{ $json[\"seniority \"] }}", "Website URL": "={{ $json.websiteURL }}", "LinkedIn URL": "={{ $json.linkedInURL }}"}, "matchingColumns": ["LinkedIn URL"], "schema": [{"id": "Full Name ", "displayName": "Full Name ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Email Address", "displayName": "Email Address", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Phone Number", "displayName": "Phone Number", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Country ", "displayName": "Country ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Location", "displayName": "Location", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Industry", "displayName": "Industry", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Company Name", "displayName": "Company Name", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Job Title", "displayName": "Job Title", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Seniority", "displayName": "Seniority", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Website URL", "displayName": "Website URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "LinkedIn URL", "displayName": "LinkedIn URL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Research Report", "displayName": "Research Report", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [1400, 0], "id": "1e412bf1-282b-4004-a9f8-e63db25458d9", "name": "Google Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "45bXNcqGMzyDwxsk", "name": "Google Sheets account"}}}, {"parameters": {"jsCode": "// Retrieve the input data from node A.\nlet inputData = $node[\"JSON\"].json;\n// If the output is an array, take the first element; otherwise, use it as is.\nif (Array.isArray(inputData)) {\n  inputData = inputData[0];\n}\n\n// Now, access the \"query\" property. \n// We expect \"query\" to be an array, so take its first element.\nconst paramsData = inputData.query[0];\n\n// Base URL for Apollo\nconst baseURL = 'https://app.apollo.io/#/people';\n\n// Array to hold each part of the query string\nconst queryParts = [];\n\n// Add static parameters\nqueryParts.push('sortByField=recommendations_score');\nqueryParts.push('sortAscending=false');\nqueryParts.push('page=1');\n\n// Helper function to process and add array parameters to queryParts\nconst addArrayParams = (paramName, values) => {\n  values.forEach(val => {\n    // Replace '+' with space then encode the value \n    // (e.g., \"sydney+australia\" -> \"sydney australia\" -> \"sydney%20australia\")\n    const decodedValue = val.replace(/\\+/g, ' ');\n    queryParts.push(`${paramName}[]=${encodeURIComponent(decodedValue)}`);\n  });\n};\n\n// Process job titles (maps to personTitles[])\nif (paramsData.job_title && Array.isArray(paramsData.job_title)) {\n  addArrayParams('personTitles', paramsData.job_title);\n}\n\n// Process locations (maps to personLocations[])\nif (paramsData.location && Array.isArray(paramsData.location)) {\n  addArrayParams('personLocations', paramsData.location);\n}\n\n// Process business keywords (maps to qOrganizationKeywordTags[])\nif (paramsData.business && Array.isArray(paramsData.business)) {\n  addArrayParams('qOrganizationKeywordTags', paramsData.business);\n}\n\n// Add static included organization keyword fields\nqueryParts.push('includedOrganizationKeywordFields[]=tags');\nqueryParts.push('includedOrganizationKeywordFields[]=name');\n\n// Combine all query parts with '&' to form the full query string\nconst queryString = queryParts.join('&');\n\n// Build the final URL\nconst finalURL = `${baseURL}?${queryString}`;\n\n// Return the output as an array of objects\nreturn [{ json: { finalURL } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 0], "id": "adecbd00-8576-4c94-9e4d-dc6637816a3f", "name": "Create URL"}, {"parameters": {"assignments": {"assignments": [{"id": "336074e3-b3b9-4dde-92d7-fb5af51b8ffa", "name": "fullName", "value": "={{ $json.first_name }} {{ $json.last_name }}", "type": "string"}, {"id": "da8362c5-75a5-4431-847d-fd4e02112bcc", "name": "emailAddress", "value": "={{ $json.email }}", "type": "string"}, {"id": "3c99e0e0-4184-4e85-baa0-8bb85e4e227b", "name": "linkedInURL", "value": "={{ $json.linkedin_url }}", "type": "string"}, {"id": "a45af1ff-7026-47a0-a42c-bdb3b27c8e3d", "name": "seniority ", "value": "={{ $json.seniority }}", "type": "string"}, {"id": "fa551406-a981-4fbb-babc-aa78ab10010d", "name": "jobTitle", "value": "={{ $json.employment_history[0].title }}", "type": "string"}, {"id": "2e8d8d61-bd02-4f24-91f1-707152d99806", "name": "companyName", "value": "={{ $json.employment_history[0].organization_name }}", "type": "string"}, {"id": "1295a702-636a-48bd-8bcd-df92162237fb", "name": "location", "value": "={{ $json.city }}, {{ $json.state }}", "type": "string"}, {"id": "0072b657-0296-4858-b190-621831943816", "name": "country", "value": "={{ $json.country }}", "type": "string"}, {"id": "22ce107c-30ef-4abe-9fab-8d49482da87c", "name": "Number", "value": "={{ $json.organization.primary_phone && $json.organization.primary_phone.sanitized_number && $json.organization.primary_phone.sanitized_number ? $json.organization.primary_phone.sanitized_number : null }}", "type": "string"}, {"id": "302b9529-e16b-49ce-bbb6-ae2de90fed93", "name": "websiteURL", "value": "={{ $json.organization_website_url }}", "type": "string"}, {"id": "13026de1-0927-44f7-8388-4d525f44974d", "name": "businessIndustry", "value": "={{ $('JSON').item.json.query[0].business[0].replaceAll('+',' ') }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [960, 0], "id": "5aca7288-0c0c-4d03-95bb-1e5367f4b522", "name": "Extract Info"}, {"parameters": {"assignments": {"assignments": [{"id": "2ecbc937-c5dc-4948-8d27-ccdb4d4a9bed", "name": "output", "value": "={{ $input.all().length }} new contacts have been added to the Google Sheet!", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1620, 0], "id": "f1f0b58e-d663-4a41-b853-693b98b0c51b", "name": "<PERSON>"}, {"parameters": {}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [1820, 0], "id": "e1a215eb-8541-4edd-8b0f-e335698cabdd", "name": "Limit"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n\"query\": \n{{ $json.query }}\n}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, 0], "id": "837502f9-060f-4ab8-ae7f-1d82fe636cb5", "name": "JSON"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.linkedInURL }}", "options": {}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [1180, 0], "id": "a6398036-4f76-400e-9116-5396e88e2c60", "name": "Remove Duplicates"}], "pinData": {"When Executed by Another Workflow": [{"json": {"query": "[\n {\n \"location\": [\n \"sydney+australia\",\n \"tokyo+japan\",\n \"new+york+united+states\"\n ],\n \"business\": [\n \"cafe\"\n ],\n \"job_title\": [\n \"ceo\",\n \"founder\"\n ]\n }\n ]"}}]}, "connections": {"When Executed by Another Workflow": {"main": [[{"node": "JSON", "type": "main", "index": 0}]]}, "Apollo Scraper": {"main": [[{"node": "Extract Info", "type": "main", "index": 0}]]}, "Create URL": {"main": [[{"node": "<PERSON>raper", "type": "main", "index": 0}]]}, "Extract Info": {"main": [[{"node": "Remove Duplicates", "type": "main", "index": 0}]]}, "Google Sheets": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "JSON": {"main": [[{"node": "Create URL", "type": "main", "index": 0}]]}, "Remove Duplicates": {"main": [[{"node": "Google Sheets", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0a9cce38-e191-4f22-9e6f-6916e71ca643", "meta": {"templateCredsSetupCompleted": true, "instanceId": "b28d66d14f40eb70ce76982ff5a26f67c6ff8a3b5e252b0e32973f57f9e31707"}, "id": "QevwTWErLIVV9QU9", "tags": []}