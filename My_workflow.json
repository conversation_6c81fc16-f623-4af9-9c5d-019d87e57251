{"name": "My workflow", "nodes": [{"parameters": {"options": {"systemMessage": "You are an AI agent designed to orchestrate social media scraping on Instagram.\n\n1.  **Analyze the User's Query:** From the user's text, identify the following entities:\n    -   The client's primary Instagram handle.\n    -   A list of target Instagram handles to scrape for viral content.\n    -   A list of target hashtags to scrape for viral content.\n    -   Keywords or a description of the desired content style.\n\n2.  **Plan Tool Use:** You have access to the following tools. Their names MUST be passed exactly as `apify-slash-TOOL_NAME`.\n    -   `apify-slash-instagram-profile-scraper`: Use this ONCE for the client's primary handle to get their bio and recent posts.\n    -   `apify-slash-instagram-hashtag-scraper`: Use this for each target hashtag.\n    -   `apify-slash-instagram-post-scraper`: Use this for each target username.\n\n3.  **Execute Tools:** Call the necessary tools to gather all the required data. Scrape up to 10-15 recent/top posts from each target source.\n\n4.  **Consolidate and Format Output:** After all scraping is complete, you MUST aggregate all the results into a single, valid JSON array. The array should contain multiple objects, where each object represents a scraped post. Each post object MUST have the following structure:\n\n    {\n      \"source_type\": \"string (either 'client_post', 'viral_hashtag_post', or 'viral_user_post')\",\n      \"source_identifier\": \"string (the client's handle, the hashtag with '#', or the target username)\",\n      \"id\": \"string (the post's unique ID from the scraper)\",\n      \"type\": \"string ('Video', 'Image', or 'Carousel')\",\n      \"post_url\": \"string (the permalink to the post, e.g., https://www.instagram.com/p/shortcode/)\",\n      \"media_urls\": [\"array\", \"of\", \"strings (direct URLs to the .jpg or .mp4 files)\"],\n      \"caption\": \"string (the full caption text)\",\n      \"hashtags\": [\"array\", \"of\", \"strings\"],\n      \"like_count\": \"number\",\n      \"view_count\": \"number (or null if not a video)\"\n    }\n\n**Do NOT output any other text, explanation, or conversational preamble. Your entire final output must be only the JSON array of post objects.**"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-160, -220], "id": "da4e7aba-8a91-4ebe-9b4e-9c34b836984f", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-220, -5], "id": "9fee72e5-6b00-42fa-8ac6-fcc94a314219", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "MH8qxTiIzNpDwdbz", "name": "OpenAi account"}}}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"tools\",\"find the relevant tool\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [80, 0], "id": "1dbb97aa-1564-4526-bdff-669b82a47ab8", "name": "MCP Client1", "credentials": {"mcpClientApi": {"id": "akrrmt6ehre9u2uP", "name": "MCP Client (STDIO) account"}}}, {"parameters": {"formTitle": "viral content", "formDescription": "one step to going viral", "formFields": {"values": [{"fieldLabel": "Instagram Handle", "placeholder": "Your Username"}, {"fieldLabel": "what Kind of content do you want?"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-760, -240], "id": "765d94b8-1eae-45d4-8d39-a<PERSON><PERSON>ef6d68", "name": "On form submission", "webhookId": "9bab70be-b7d9-41d9-be78-d1ae0e6bc10d"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-440, -225], "id": "628d1592-2597-4939-b724-8beff5ee656c", "name": "When chat message received", "webhookId": "349c4841-9435-4974-93dd-0c2108ee6f6d"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-120, -40], "id": "58824f77-c573-46ab-b11f-13c887c105f3", "name": "Simple Memory"}, {"parameters": {"modelId": "gpt-4o", "messages": {"values": [{"content": "You are an expert social media analyst. You will be given the caption, hashtags, and a media analysis (if available) for an Instagram post. Combine all this information to provide a comprehensive summary of the post's content, style, tone, and potential engagement drivers.", "role": "system"}, {"content": "=Post ID: {{ $json.id || $json.shortCode || 'N/A' }}\nCaption: {{ $json.caption || 'No caption' }}\nHashtags: {{ $json.hashtags ? $json.hashtags.join(', ') : 'None' }}\n{{ $json.video_transcript ? 'Video Transcript Summary: ' + $json.video_transcript : '' }}\n{{ $json.image_analysis ? 'Image Analysis: ' + $json.image_analysis : '' }}"}]}, "options": {"temperature": 0.5}}, "id": "680a0849-0c7d-4cba-8a8d-0cfc159d591b", "name": "6. OpenAI: Summarize Full Post", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [3560, -1140], "credentials": {"openAiApi": {"id": "MH8qxTiIzNpDwdbz", "name": "OpenAi account"}}, "continueOnFail": true}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [760, -220], "id": "24f914bb-ffdf-4e4b-affa-3ce094f6245f", "name": "Loop Over Items"}, {"parameters": {"jsCode": "// This script assumes the AI Agent outputs ONE item.\n// That item's json has a property (e.g., 'output') containing the JSON string.\nconst agentOutputString = $input.first().json.output;\n\n// Parse the string into a JavaScript array of post objects\nconst postsArray = JSON.parse(agentOutputString);\n\n// Return the array of posts.\n// n8n will automatically convert this into multiple output items,\n// one for each post in the array.\nreturn postsArray.map(post => ({ json: post }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, -220], "id": "836c9f62-a064-4a11-85b4-728f274bd02e", "name": "Code"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0fa5f463-3638-4024-837f-8ee4cf6aa429", "leftValue": "={{ $json.type.toLowerCase()}}", "rightValue": "carousel", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1060, -220], "id": "93986657-3f9c-4a24-a9f0-b32e7910e8e4", "name": "Is Carousel"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "CHATGPT-4O-LATEST"}, "text": "You are an expert visual analyst specializing in identifying elements of viral social media content in the health and wellness niche.\nAnalyze the provided Instagram image. Focus on:\n1.  **Overall Style/Aesthetic** and how it contributes to appeal.\n2.  **Subject Matter & Composition** and why it might be engaging.\n3.  **Color Palette & Lighting** and their impact.\n4.  **Mood/Vibe Conveyed.**\n5.  **Key Visual Elements that likely contribute to its virality** (e.g., striking imagery, relatability, aspirational quality, clarity of message if visual).\nBe concise and descriptive. Output only your analysis.", "imageUrls": "={{ $json.media_urls[0] }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2140, -320], "id": "05e4f689-c54a-4a17-8c90-8d98f724e6ec", "name": "OpenAI1", "credentials": {"openAiApi": {"id": "MH8qxTiIzNpDwdbz", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a2b1122b-aeca-4e72-8d42-16133333c85e", "leftValue": "={{ $json.type.toLowerCase() }}", "rightValue": "video", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1460, 60], "id": "dccb6876-d819-45b4-9760-614a8f41971c", "name": "Is Video?1"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "CHATGPT-4O-LATEST"}, "text": "You are an expert visual analyst specializing in identifying elements of viral social media content in the health and wellness niche.\nAnalyze the provided Instagram image. Focus on:\n1.  **Overall Style/Aesthetic** and how it contributes to appeal.\n2.  **Subject Matter & Composition** and why it might be engaging.\n3.  **Color Palette & Lighting** and their impact.\n4.  **Mood/Vibe Conveyed.**\n5.  **Key Visual Elements that likely contribute to its virality** (e.g., striking imagery, relatability, aspirational quality, clarity of message if visual).\nBe concise and descriptive. Output only your analysis.", "imageUrls": "={{ $json.media_urls[0] }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1740, 200], "id": "9a9a8799-5d67-4091-8f56-0306aa161b39", "name": "OpenAI2", "credentials": {"openAiApi": {"id": "MH8qxTiIzNpDwdbz", "name": "OpenAi account"}}, "onError": "continueErrorOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "set_transcript-pes-v3", "name": "video_transcript", "type": "string", "value": "={{ $json.transcript}}"}, {"id": "d52ec436-bb2f-4221-b05d-8925dde29f90", "name": "durationSec", "value": "={{ $json.durationSec }}", "type": "number"}, {"id": "d8826197-fafb-4c95-b586-4e93c2389443", "name": "source_type", "value": "={{ $('Code').item.json.source_type }}", "type": "string"}, {"id": "d19f1334-3646-457e-8a36-f6067da460b5", "name": "source_identifier", "value": "={{ $('Code').item.json.source_identifier }}", "type": "string"}, {"id": "44f2896c-f782-4175-a961-59fcad225971", "name": "type", "value": "={{ $('Code').item.json.type }}", "type": "string"}, {"id": "c935c3ed-1401-4c5f-a6d8-377ba0967694", "name": "post_url", "value": "={{ $('Code').item.json.post_url }}", "type": "string"}, {"id": "816a03de-a18f-46e9-a775-8822b9c0ab8c", "name": "caption", "value": "={{ $('Code').item.json.caption }}", "type": "string"}, {"id": "42b94b13-37f6-4e59-9281-c08f7589025f", "name": "hashtags", "value": "={{ $('Code').item.json.hashtags }}", "type": "array"}, {"id": "61274c43-fb26-4dc1-afe9-f45b4f5a559c", "name": "like_count", "value": "={{ $('Code').item.json.like_count }}", "type": "number"}, {"id": "6ed8e832-9ced-42fe-a249-1ef6ecd09a89", "name": "view_count", "value": "={{ $('Code').item.json.view_count }}", "type": "number"}]}, "options": {}}, "id": "b1c15afe-f6c1-4afb-86ca-1a9a9fac9e73", "name": "4a. Set: Add Transcript2", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1960, -20]}, {"parameters": {"assignments": {"assignments": [{"id": "9561c0b3-fd12-490f-be0b-7d6b287819c7", "name": "content", "value": "={{ $json.content }}", "type": "string"}, {"id": "4d682e28-601b-4f73-afb8-77f782e14f55", "name": "source_type", "value": "={{ $('Code').item.json.source_type }}", "type": "string"}, {"id": "f263bf56-7d74-491e-82c7-dfd8de9bea65", "name": "source_identifier", "value": "={{ $('Code').item.json.source_identifier }}", "type": "string"}, {"id": "96df13ec-5215-4d20-9092-187a620eff18", "name": "type", "value": "={{ $('Code').item.json.type }}", "type": "string"}, {"id": "3356a7d9-2a19-4100-a878-9fcd3e31045b", "name": "post_url", "value": "={{ $('Code').item.json.post_url }}", "type": "string"}, {"id": "32b4d00f-6235-4d60-a942-83a591bc665e", "name": "media_urls", "value": "={{ $('Code').item.json.media_urls }}", "type": "array"}, {"id": "a6876711-2a11-4f75-8e59-61f1416b2296", "name": "caption", "value": "={{ $('Code').item.json.caption }}", "type": "string"}, {"id": "a9f369a8-1d79-4e08-90e1-49fca15a2cd0", "name": "hashtags", "value": "={{ $('Code').item.json.hashtags }}", "type": "array"}, {"id": "31acd1f2-bc2a-4a27-ab19-98008bf2cc1f", "name": "like_count", "value": "={{ $('Code').item.json.like_count }}", "type": "number"}, {"id": "23491cf4-2032-453a-a393-30222d8f7c6b", "name": "view_count", "value": "={{ $('Code').item.json.view_count }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "2a9d4daf-c8b5-4b2d-9f2a-bbe57fb04c42", "name": "4b. Set: Add Image Analysis2", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1960, 200]}, {"parameters": {"url": "https://api.apify.com/v2/acts/tictechid~anoxvanzi-transcriber/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.media_urls[0] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2140, -620], "id": "21848fc1-c6e0-4541-afcb-656e777c8642", "name": "HTTP Request3"}, {"parameters": {"content": "AI agent with the mcp server", "height": 600, "width": 840}, "type": "n8n-nodes-base.stickyNote", "position": [-520, -460], "typeVersion": 1, "id": "5bda08fe-707b-4102-99e5-c976c0cb7e3d", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "User submits form", "height": 600, "width": 300, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-860, -460], "typeVersion": 1, "id": "7a57a5a1-731a-4099-94e5-9255d418403a", "name": "Sticky Note1"}, {"parameters": {"content": "Converts JSON String to JSON Array", "height": 600, "width": 300, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [340, -460], "typeVersion": 1, "id": "e0bc03ab-6530-474e-8ea9-ffae68c27d7d", "name": "Sticky Note2"}, {"parameters": {"content": "run each item in array in loop", "height": 600, "width": 300, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [660, -460], "typeVersion": 1, "id": "0a39755c-3f38-4f28-a835-2c361ab4698f", "name": "Sticky Note3"}, {"parameters": {"content": "Checks <PERSON> first", "height": 600, "width": 300}, "type": "n8n-nodes-base.stickyNote", "position": [980, -460], "typeVersion": 1, "id": "dd39e4b8-dddb-406d-8d65-9a349fdd539c", "name": "Sticky Note4"}, {"parameters": {"content": "Audio and video summarization of carousal posts", "height": 660, "width": 1420}, "type": "n8n-nodes-base.stickyNote", "position": [1380, -860], "typeVersion": 1, "id": "4f455ea6-5cf1-46b2-a649-b4971a0f79e4", "name": "Sticky Note5"}, {"parameters": {"content": "Audio and video summarization of only video and image post", "height": 480, "width": 1420, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [1380, -120], "typeVersion": 1, "id": "e1ef452b-4c1f-4df5-9f88-5fb3ad990c23", "name": "Sticky Note6"}, {"parameters": {"content": "final summarizartion", "height": 280, "width": 480, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [3480, -1240], "typeVersion": 1, "id": "529887f9-1c60-444e-90cb-4cdf31ddd0c7", "name": "Sticky Note7"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1620, -480], "id": "4a248d51-b717-434c-93e0-996dda40452e", "name": "Loop Over Carousel Media", "alwaysOutputData": false}, {"parameters": {"jsCode": "// This script receives one item from the previous node.\n// It checks the 'media_urls' array within that item's JSON.\n\n// Use .first() to get the first (and only) input item.\nconst item = $input.first();\nconst post = item.json;\n\nlet hasVideo = false; // Default to false\n\n// Ensure media_urls exists and is an array before looping\nif (post.media_urls && Array.isArray(post.media_urls)) {\n  // Loop through the array of URL strings\n  for (const url of post.media_urls) {\n    // Check if the url is a string and contains '.mp4'\n    if (typeof url === 'string' && url.includes('.mp4')) {\n      hasVideo = true;\n      // We found a video, so we can stop checking the rest of the URLs\n      break; \n    }\n  }\n}\n\n// Add the result as a new property TO THE SAME ITEM.\n// This is crucial. We are modifying the item, not creating a new one.\nitem.json.carousel_contains_video = hasVideo;\n\n// Return the modified item, wrapped in an array as n8n expects.\nreturn [item];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1420, -460], "id": "cf84a385-a827-4dbc-9f04-99273009f2a8", "name": "determine media type"}, {"parameters": {"url": "https://api.apify.com/v2/acts/tictechid~anoxvanzi-transcriber/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"start_urls\": \"{{ $json.media_urls[0] }}\",\n    \"useProxy\": false\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1740, -20], "id": "89d62f56-6adb-478e-9873-f3909e734b4e", "name": "Transcribe Video", "executeOnce": false, "retryOnFail": true}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [2580, -500], "id": "27424003-ba0a-4498-90cb-5448ccbca74b", "name": "End of Carousel Media"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a2b1122b-aeca-4e72-8d42-16133333c85e", "leftValue": "={{ $json.carousel_contains_video }}", "rightValue": ".jpg?", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1860, -460], "id": "8112bbb7-449a-496f-ab68-f414e2a0cffe", "name": "Is Video?", "alwaysOutputData": false}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [2100, -820], "id": "329e9d51-96c5-41a9-a913-a22427c398f4", "name": "Aggregate Carousel Analyses", "alwaysOutputData": false}, {"parameters": {"assignments": {"assignments": [{"id": "d5448166-ffe9-453c-984e-9c31b9b44e1c", "name": "content[0]", "value": "={{ $json.content[0] }}", "type": "string"}, {"id": "33769ac3-0957-42e9-9bf0-b243c34ee314", "name": "source_type", "value": "={{ $('Is Carousel').item.json.source_type }}", "type": "string"}, {"id": "fcbc9f06-927a-494d-bfa0-fa446a761184", "name": "source_identifier", "value": "={{ $('Is Carousel').item.json.source_identifier }}", "type": "string"}, {"id": "47767427-c36f-4618-8fa8-ba23223316d9", "name": "post_url", "value": "={{ $('Is Carousel').item.json.post_url }}", "type": "string"}, {"id": "17b3dfa0-a2d3-4e1a-a18b-ae69b038279a", "name": "type", "value": "={{ $('Is Carousel').item.json.type }}", "type": "string"}, {"id": "bedfbb69-441d-4667-a66a-3e7971d1eec7", "name": "media_urls", "value": "={{ $('Is Carousel').item.json.media_urls }}", "type": "array"}, {"id": "65669e48-ee69-4c07-962c-c7e76e95a5ce", "name": "caption", "value": "={{ $('Is Carousel').item.json.caption }}", "type": "string"}, {"id": "a309dce9-3e65-4549-ad78-34996f975ad0", "name": "hashtags", "value": "={{ $('Is Carousel').item.json.hashtags }}", "type": "array"}, {"id": "897f6487-d476-408e-b930-6f63c3733b08", "name": "like_count", "value": "={{ $('Is Carousel').item.json.like_count }}", "type": "number"}, {"id": "b894c603-c4e5-4c50-839f-69b119f563c1", "name": "view_count", "value": "={{ $('Is Carousel').item.json.view_count }}", "type": "string"}]}, "options": {}}, "id": "cfc93994-9216-436a-b235-bae2c415c1b4", "name": "Enriched Carousel Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2500, -820]}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [2980, -240], "id": "843657b3-4ca8-4838-8d01-e04383be964f", "name": "Merge All Enriched Posts"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content"}, {"fieldToAggregate": "video_transcript"}, {"fieldToAggregate": "durationSec"}, {"fieldToAggregate": "post_url"}, {"fieldToAggregate": "hashtags"}, {"fieldToAggregate": "like_count"}, {"fieldToAggregate": "caption"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1300, -1120], "id": "29acdd6d-a0eb-4782-aa1d-bf4d4129f86b", "name": "done"}, {"parameters": {"content": "collect all", "height": 280, "width": 320, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [2880, -320], "typeVersion": 1, "id": "006771d8-ed74-41c9-a6e2-bdcf48500291", "name": "Sticky Note8"}, {"parameters": {"content": "to call another agent", "height": 280, "width": 320, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [1200, -1200], "typeVersion": 1, "id": "b4eb4a74-55d2-416e-9aa8-45d768465ae4", "name": "Sticky Note9"}, {"parameters": {}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [200, -40], "id": "0303ec54-5d68-4bc4-a034-ac36288ddb33", "name": "MCP Client", "credentials": {"mcpClientApi": {"id": "akrrmt6ehre9u2uP", "name": "MCP Client (STDIO) account"}}}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [2300, 80], "id": "3b787912-37e8-49dd-8993-763182b23b23", "name": "Merge All Enriched Posts from single post"}, {"parameters": {"assignments": {"assignments": [{"id": "9561c0b3-fd12-490f-be0b-7d6b287819c7", "name": "content", "value": "={{ $json.content }}", "type": "string"}, {"id": "4d682e28-601b-4f73-afb8-77f782e14f55", "name": "source_type", "value": "={{ $('Code').item.json.source_type }}", "type": "string"}, {"id": "f263bf56-7d74-491e-82c7-dfd8de9bea65", "name": "source_identifier", "value": "={{ $('Code').item.json.source_identifier }}", "type": "string"}, {"id": "96df13ec-5215-4d20-9092-187a620eff18", "name": "type", "value": "={{ $('Code').item.json.type }}", "type": "string"}, {"id": "3356a7d9-2a19-4100-a878-9fcd3e31045b", "name": "post_url", "value": "={{ $('Code').item.json.post_url }}", "type": "string"}, {"id": "32b4d00f-6235-4d60-a942-83a591bc665e", "name": "media_urls", "value": "={{ $('Code').item.json.media_urls }}", "type": "array"}, {"id": "a6876711-2a11-4f75-8e59-61f1416b2296", "name": "caption", "value": "={{ $('Code').item.json.caption }}", "type": "string"}, {"id": "a9f369a8-1d79-4e08-90e1-49fca15a2cd0", "name": "hashtags", "value": "={{ $('Code').item.json.hashtags }}", "type": "array"}, {"id": "31acd1f2-bc2a-4a27-ab19-98008bf2cc1f", "name": "like_count", "value": "={{ $('Code').item.json.like_count }}", "type": "number"}, {"id": "23491cf4-2032-453a-a393-30222d8f7c6b", "name": "view_count", "value": "={{ $('Code').item.json.view_count }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "e0f3b895-061f-4679-a479-334b390d59e6", "name": "4b. Set: Add Image Analysis", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2380, -320]}, {"parameters": {"assignments": {"assignments": [{"id": "set_transcript-pes-v3", "name": "video_transcript", "type": "string", "value": "={{ $json.transcript}}"}, {"id": "d52ec436-bb2f-4221-b05d-8925dde29f90", "name": "durationSec", "value": "={{ $json.durationSec }}", "type": "number"}, {"id": "d8826197-fafb-4c95-b586-4e93c2389443", "name": "source_type", "value": "={{ $('Code').item.json.source_type }}", "type": "string"}, {"id": "d19f1334-3646-457e-8a36-f6067da460b5", "name": "source_identifier", "value": "={{ $('Code').item.json.source_identifier }}", "type": "string"}, {"id": "44f2896c-f782-4175-a961-59fcad225971", "name": "type", "value": "={{ $('Code').item.json.type }}", "type": "string"}, {"id": "c935c3ed-1401-4c5f-a6d8-377ba0967694", "name": "post_url", "value": "={{ $('Code').item.json.post_url }}", "type": "string"}, {"id": "816a03de-a18f-46e9-a775-8822b9c0ab8c", "name": "caption", "value": "={{ $('Code').item.json.caption }}", "type": "string"}, {"id": "42b94b13-37f6-4e59-9281-c08f7589025f", "name": "hashtags", "value": "={{ $('Code').item.json.hashtags }}", "type": "array"}, {"id": "61274c43-fb26-4dc1-afe9-f45b4f5a559c", "name": "like_count", "value": "={{ $('Code').item.json.like_count }}", "type": "number"}, {"id": "6ed8e832-9ced-42fe-a249-1ef6ecd09a89", "name": "view_count", "value": "={{ $('Code').item.json.view_count }}", "type": "number"}]}, "options": {}}, "id": "6c75250a-ead9-4024-988a-32ccfddfa496", "name": "4a. Set: Add Transcript", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2360, -620]}, {"parameters": {"assignments": {"assignments": [{"id": "707b1a6d-5bf8-442d-9303-3994d880a361", "name": "content", "value": "={{ $json.content }}", "type": "array"}, {"id": "57864265-d006-491c-b505-9da46db36fca", "name": "durationSec", "value": "={{ $json.durationSec }}", "type": "array"}, {"id": "a60438f7-fd85-4510-ba98-4f1468d01a22", "name": "hashtags", "value": "={{ $json.hashtags }}", "type": "array"}, {"id": "8c47dffa-d6cd-4143-9fbf-854b328a657d", "name": "like_count", "value": "={{ $json.like_count }}", "type": "array"}, {"id": "a1f1be4d-a337-46e6-bf03-7f924204ae30", "name": "caption", "value": "={{ $json.caption }}", "type": "array"}]}, "options": {}}, "id": "44746bf4-4b49-4a80-a50e-fa188b7d750f", "name": "Enriched Carousel Data1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2280, -1160]}, {"parameters": {"content": "set nodes", "height": 280, "width": 320, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [2180, -1240], "typeVersion": 1, "id": "76c6da5b-7f3d-4f09-b029-b8f5fdb26b24", "name": "Sticky Note10"}, {"parameters": {"content": "200 sucess", "height": 600, "width": 300, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-1200, -460], "typeVersion": 1, "id": "3f9b5f4c-fee4-4ec4-a95f-90a0bd5e5466", "name": "Sticky Note11"}, {"parameters": {"content": "webhook", "height": 600, "width": 300, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-1540, -460], "typeVersion": 1, "id": "75b8a9ff-ecbb-499e-b506-d6a5e4dd8add", "name": "Sticky Note12"}, {"parameters": {"path": "5c18f34c-7b23-4691-a543-90755d168f5f", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1460, -220], "id": "b150c60b-2550-4749-967b-133d8a381df6", "name": "Webhook", "webhookId": "5c18f34c-7b23-4691-a543-90755d168f5f"}, {"parameters": {"options": {"systemMessage": "You are an AI agent designed to orchestrate social media scraping on Instagram.\n\n1.  **Analyze the User's Query:** From the user's text, identify the following entities:\n    -   The client's primary Instagram handle.\n    -   A list of target Instagram handles to scrape for viral content.\n    -   A list of target hashtags to scrape for viral content.\n    -   Keywords or a description of the desired content style.\n\n2.  **Plan Tool Use:** You have access to the following tools. Their names MUST be passed exactly as `apify-slash-TOOL_NAME`.\n    -   `apify-slash-instagram-profile-scraper`: Use this ONCE for the client's primary handle to get their bio and recent posts.\n    -   `apify-slash-instagram-hashtag-scraper`: Use this for each target hashtag.\n    -   `apify-slash-instagram-post-scraper`: Use this for each target username.\n\n3.  **Execute Tools:** Call the necessary tools to gather all the required data. Scrape up to 10-15 recent/top posts from each target source.\n\n4.  **Consolidate and Format Output:** After all scraping is complete, you MUST aggregate all the results into a single, valid JSON array. The array should contain multiple objects, where each object represents a scraped post. Each post object MUST have the following structure:\n\n    {\n      \"source_type\": \"string (either 'client_post', 'viral_hashtag_post', or 'viral_user_post')\",\n      \"source_identifier\": \"string (the client's handle, the hashtag with '#', or the target username)\",\n      \"id\": \"string (the post's unique ID from the scraper)\",\n      \"type\": \"string ('Video', 'Image', or 'Carousel')\",\n      \"post_url\": \"string (the permalink to the post, e.g., https://www.instagram.com/p/shortcode/)\",\n      \"media_urls\": [\"array\", \"of\", \"strings (direct URLs to the .jpg or .mp4 files)\"],\n      \"caption\": \"string (the full caption text)\",\n      \"hashtags\": [\"array\", \"of\", \"strings\"],\n      \"like_count\": \"number\",\n      \"view_count\": \"number (or null if not a video)\"\n    }\n\n**Do NOT output any other text, explanation, or conversational preamble. Your entire final output must be only the JSON array of post objects.**"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-360, 2020], "id": "4da009bb-0c39-44ed-8bac-6c6f25f85397", "name": "AI Agent2"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-500, 2280], "id": "d90bea70-327b-44a3-ab00-0f86f60746ed", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "MH8qxTiIzNpDwdbz", "name": "OpenAi account"}}}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"tools\",\"find the relevant tool\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [-180, 2260], "id": "ab509280-b34d-4ad4-9cc3-762762e39b46", "name": "MCP Client4", "credentials": {"mcpClientApi": {"id": "akrrmt6ehre9u2uP", "name": "MCP Client (STDIO) account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-380, 2280], "id": "67bd158b-8f20-4e6b-9630-2cf00c8037c2", "name": "Simple Memory2"}, {"parameters": {"content": "AI agent with the mcp server", "height": 600, "width": 840}, "type": "n8n-nodes-base.stickyNote", "position": [-660, 1800], "typeVersion": 1, "id": "8c4d174d-0727-4ca3-8a7a-a0e4bddf2a3c", "name": "Sticky Note13"}, {"parameters": {}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [-80, 2240], "id": "ca09d155-7895-4d1e-b9fc-9833aa185b9b", "name": "MCP Client5", "credentials": {"mcpClientApi": {"id": "akrrmt6ehre9u2uP", "name": "MCP Client (STDIO) account"}}}, {"parameters": {"modelId": "gpt-4o", "messages": {"values": [{"content": "You are an expert social media analyst. You will be given the caption, hashtags, and a media analysis (if available) for an Instagram post. Combine all this information to provide a comprehensive summary of the post's content, style, tone, and potential engagement drivers.", "role": "system"}, {"content": "=Post ID: {{ $json.id || $json.shortCode || 'N/A' }}\nCaption: {{ $json.caption || 'No caption' }}\nHashtags: {{ $json.hashtags ? $json.hashtags.join(', ') : 'None' }}\n{{ $json.video_transcript ? 'Video Transcript Summary: ' + $json.video_transcript : '' }}\n{{ $json.image_analysis ? 'Image Analysis: ' + $json.image_analysis : '' }}"}]}, "options": {"temperature": 0.5}}, "id": "8fc3e433-a93b-4508-b4a5-e4d166a455f1", "name": "6. OpenAI: Summarize Full Post2", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [3440, 1100], "credentials": {"openAiApi": {"id": "MH8qxTiIzNpDwdbz", "name": "OpenAi account"}}, "continueOnFail": true}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [640, 2020], "id": "3ee0ba0c-cac6-4c3f-975f-f350cd1ab305", "name": "Loop Over Items1"}, {"parameters": {"jsCode": "// This script assumes the AI Agent outputs ONE item.\n// That item's json has a property (e.g., 'output') containing the JSON string.\nconst agentOutputString = $input.first().json.output;\n\n// Parse the string into a JavaScript array of post objects\nconst postsArray = JSON.parse(agentOutputString);\n\n// Return the array of posts.\n// n8n will automatically convert this into multiple output items,\n// one for each post in the array.\nreturn postsArray.map(post => ({ json: post }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [320, 2020], "id": "e600e0a4-224f-48e1-8fc6-04075466cf09", "name": "Code1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0fa5f463-3638-4024-837f-8ee4cf6aa429", "leftValue": "={{ $json.type.toLowerCase()}}", "rightValue": "carousel", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [940, 2020], "id": "59f449fe-4cff-4bfe-a9c1-d1e89e83e76a", "name": "Is Carousel1"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "CHATGPT-4O-LATEST"}, "text": "You are an expert visual analyst specializing in identifying elements of viral social media content in the health and wellness niche.\nAnalyze the provided Instagram image. Focus on:\n1.  **Overall Style/Aesthetic** and how it contributes to appeal.\n2.  **Subject Matter & Composition** and why it might be engaging.\n3.  **Color Palette & Lighting** and their impact.\n4.  **Mood/Vibe Conveyed.**\n5.  **Key Visual Elements that likely contribute to its virality** (e.g., striking imagery, relatability, aspirational quality, clarity of message if visual).\nBe concise and descriptive. Output only your analysis.", "imageUrls": "={{ $json.media_urls[0] }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [2020, 1920], "id": "c4cb333a-e96d-470c-b605-e688746205ee", "name": "OpenAI", "credentials": {"openAiApi": {"id": "MH8qxTiIzNpDwdbz", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a2b1122b-aeca-4e72-8d42-16133333c85e", "leftValue": "={{ $json.type.toLowerCase() }}", "rightValue": "video", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1340, 2300], "id": "51bf1ab1-ee0a-4226-8e59-dadcbcaf4d54", "name": "Is Video?2"}, {"parameters": {"resource": "image", "operation": "analyze", "modelId": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "CHATGPT-4O-LATEST"}, "text": "You are an expert visual analyst specializing in identifying elements of viral social media content in the health and wellness niche.\nAnalyze the provided Instagram image. Focus on:\n1.  **Overall Style/Aesthetic** and how it contributes to appeal.\n2.  **Subject Matter & Composition** and why it might be engaging.\n3.  **Color Palette & Lighting** and their impact.\n4.  **Mood/Vibe Conveyed.**\n5.  **Key Visual Elements that likely contribute to its virality** (e.g., striking imagery, relatability, aspirational quality, clarity of message if visual).\nBe concise and descriptive. Output only your analysis.", "imageUrls": "={{ $json.media_urls[0] }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1620, 2440], "id": "dd85f089-c586-4805-afeb-c545118eaef5", "name": "OpenAI3", "credentials": {"openAiApi": {"id": "MH8qxTiIzNpDwdbz", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "set_transcript-pes-v3", "name": "video_transcript", "type": "string", "value": "={{ $json.transcript}}"}, {"id": "d52ec436-bb2f-4221-b05d-8925dde29f90", "name": "durationSec", "value": "={{ $json.durationSec }}", "type": "number"}, {"id": "d8826197-fafb-4c95-b586-4e93c2389443", "name": "source_type", "value": "={{ $('Code1').item.json.source_type }}", "type": "string"}, {"id": "d19f1334-3646-457e-8a36-f6067da460b5", "name": "source_identifier", "value": "={{ $('Code1').item.json.source_identifier }}", "type": "string"}, {"id": "44f2896c-f782-4175-a961-59fcad225971", "name": "type", "value": "={{ $('Code1').item.json.type }}", "type": "string"}, {"id": "c935c3ed-1401-4c5f-a6d8-377ba0967694", "name": "post_url", "value": "={{ $('Code1').item.json.post_url }}", "type": "string"}, {"id": "816a03de-a18f-46e9-a775-8822b9c0ab8c", "name": "caption", "value": "={{ $('Code1').item.json.caption }}", "type": "string"}, {"id": "42b94b13-37f6-4e59-9281-c08f7589025f", "name": "hashtags", "value": "={{ $('Code1').item.json.hashtags }}", "type": "array"}, {"id": "61274c43-fb26-4dc1-afe9-f45b4f5a559c", "name": "like_count", "value": "={{ $('Code1').item.json.like_count }}", "type": "number"}, {"id": "6ed8e832-9ced-42fe-a249-1ef6ecd09a89", "name": "view_count", "value": "={{ $('Code1').item.json.view_count }}", "type": "number"}]}, "options": {}}, "id": "c6201dc5-d099-459e-8608-6c657db06a48", "name": "4a. Set: Add Transcript3", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1840, 2220]}, {"parameters": {"assignments": {"assignments": [{"id": "9561c0b3-fd12-490f-be0b-7d6b287819c7", "name": "content", "value": "={{ $json.content }}", "type": "string"}, {"id": "4d682e28-601b-4f73-afb8-77f782e14f55", "name": "source_type", "value": "={{ $('Code1').item.json.source_type }}", "type": "string"}, {"id": "f263bf56-7d74-491e-82c7-dfd8de9bea65", "name": "source_identifier", "value": "={{ $('Code1').item.json.source_identifier }}", "type": "string"}, {"id": "96df13ec-5215-4d20-9092-187a620eff18", "name": "type", "value": "={{ $('Code1').item.json.type }}", "type": "string"}, {"id": "3356a7d9-2a19-4100-a878-9fcd3e31045b", "name": "post_url", "value": "={{ $('Code1').item.json.post_url }}", "type": "string"}, {"id": "32b4d00f-6235-4d60-a942-83a591bc665e", "name": "media_urls", "value": "={{ $('Code1').item.json.media_urls }}", "type": "array"}, {"id": "a6876711-2a11-4f75-8e59-61f1416b2296", "name": "caption", "value": "={{ $('Code1').item.json.caption }}", "type": "string"}, {"id": "a9f369a8-1d79-4e08-90e1-49fca15a2cd0", "name": "hashtags", "value": "={{ $('Code1').item.json.hashtags }}", "type": "array"}, {"id": "31acd1f2-bc2a-4a27-ab19-98008bf2cc1f", "name": "like_count", "value": "={{ $('Code1').item.json.like_count }}", "type": "number"}, {"id": "23491cf4-2032-453a-a393-30222d8f7c6b", "name": "view_count", "value": "={{ $('Code1').item.json.view_count }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "4cead4fb-0ea6-49ec-a2f3-5c22cfa074bb", "name": "4b. Set: Add Image Analysis3", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1840, 2440]}, {"parameters": {"url": "https://api.apify.com/v2/acts/tictechid~anoxvanzi-transcriber/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json.media_urls[0] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2020, 1620], "id": "5e27c7e7-f6a3-4ff2-91e2-2a8ad524acbe", "name": "HTTP Request"}, {"parameters": {"content": "run each item in array in loop", "height": 600, "width": 300, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [540, 1780], "typeVersion": 1, "id": "404b9f37-c323-406b-9f6b-d2b32e26c12a", "name": "Sticky Note14"}, {"parameters": {"content": "Checks <PERSON> first", "height": 600, "width": 300}, "type": "n8n-nodes-base.stickyNote", "position": [860, 1780], "typeVersion": 1, "id": "92e9b77e-0b77-4c3d-8b4f-46a3d25733f8", "name": "Sticky Note15"}, {"parameters": {"content": "Audio and video summarization of carousal posts", "height": 660, "width": 1420}, "type": "n8n-nodes-base.stickyNote", "position": [1260, 1380], "typeVersion": 1, "id": "d81aa064-28dd-4cab-9e81-699b7aac30aa", "name": "Sticky Note16"}, {"parameters": {"content": "Audio and video summarization of only video and image post", "height": 480, "width": 1420, "color": 5}, "type": "n8n-nodes-base.stickyNote", "position": [1260, 2120], "typeVersion": 1, "id": "47c54836-81f5-4f7b-8a84-9b4735d1d661", "name": "Sticky Note17"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1500, 1760], "id": "a840a2ea-0ad2-4043-beed-7eced54c966d", "name": "Loop Over Carousel Media1", "alwaysOutputData": false}, {"parameters": {"jsCode": "// This script receives one item from the previous node.\n// It checks the 'media_urls' array within that item's JSON.\n\n// Use .first() to get the first (and only) input item.\nconst item = $input.first();\nconst post = item.json;\n\nlet hasVideo = false; // Default to false\n\n// Ensure media_urls exists and is an array before looping\nif (post.media_urls && Array.isArray(post.media_urls)) {\n  // Loop through the array of URL strings\n  for (const url of post.media_urls) {\n    // Check if the url is a string and contains '.mp4'\n    if (typeof url === 'string' && url.includes('.mp4')) {\n      hasVideo = true;\n      // We found a video, so we can stop checking the rest of the URLs\n      break; \n    }\n  }\n}\n\n// Add the result as a new property TO THE SAME ITEM.\n// This is crucial. We are modifying the item, not creating a new one.\nitem.json.carousel_contains_video = hasVideo;\n\n// Return the modified item, wrapped in an array as n8n expects.\nreturn [item];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1300, 1780], "id": "dd0e9ae7-0775-40e3-9328-144c33bc997c", "name": "determine media type1"}, {"parameters": {"url": "https://api.apify.com/v2/acts/tictechid~anoxvanzi-transcriber/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"start_urls\": \"{{ $json.media_urls[0] }}\",\n    \"useProxy\": false\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1620, 2220], "id": "11a6a81e-923d-4c03-98ab-2d0a1571c99e", "name": "Transcribe Video1", "executeOnce": false, "retryOnFail": true}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [2460, 1740], "id": "d97af91f-293f-47ba-b5d2-b36c54c28433", "name": "End of Carousel Media1"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a2b1122b-aeca-4e72-8d42-16133333c85e", "leftValue": "={{ $json.carousel_contains_video }}", "rightValue": ".jpg?", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1740, 1780], "id": "a4ef6995-9b8e-47a9-89f0-999219fc2b44", "name": "Is Video?3", "alwaysOutputData": false}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1980, 1420], "id": "a4ab0954-109d-4e08-b45d-0c52474b7eac", "name": "Aggregate Carousel Analyses1", "alwaysOutputData": false}, {"parameters": {"assignments": {"assignments": [{"id": "d5448166-ffe9-453c-984e-9c31b9b44e1c", "name": "content[0]", "value": "={{ $json.content[0] }}", "type": "string"}, {"id": "33769ac3-0957-42e9-9bf0-b243c34ee314", "name": "source_type", "value": "={{ $('Is Carousel1').item.json.source_type }}", "type": "string"}, {"id": "fcbc9f06-927a-494d-bfa0-fa446a761184", "name": "source_identifier", "value": "={{ $('Is Carousel1').item.json.source_identifier }}", "type": "string"}, {"id": "47767427-c36f-4618-8fa8-ba23223316d9", "name": "post_url", "value": "={{ $('Is Carousel1').item.json.post_url }}", "type": "string"}, {"id": "17b3dfa0-a2d3-4e1a-a18b-ae69b038279a", "name": "type", "value": "={{ $('Is Carousel1').item.json.type }}", "type": "string"}, {"id": "bedfbb69-441d-4667-a66a-3e7971d1eec7", "name": "media_urls", "value": "={{ $('Is Carousel1').item.json.media_urls }}", "type": "array"}, {"id": "65669e48-ee69-4c07-962c-c7e76e95a5ce", "name": "caption", "value": "={{ $('Is Carousel1').item.json.caption }}", "type": "string"}, {"id": "a309dce9-3e65-4549-ad78-34996f975ad0", "name": "hashtags", "value": "={{ $('Is Carousel1').item.json.hashtags }}", "type": "array"}, {"id": "897f6487-d476-408e-b930-6f63c3733b08", "name": "like_count", "value": "={{ $('Is Carousel1').item.json.like_count }}", "type": "number"}, {"id": "b894c603-c4e5-4c50-839f-69b119f563c1", "name": "view_count", "value": "={{ $('Is Carousel1').item.json.view_count }}", "type": "string"}]}, "options": {}}, "id": "2d64280a-4930-49a0-9fdc-839cb35dac89", "name": "Enriched Carousel Data2", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2380, 1420]}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [2860, 2000], "id": "ac75042b-e3ef-4a22-893a-44e757379880", "name": "Merge All Enriched Posts1"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{"fieldToAggregate": "content"}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1180, 1120], "id": "dac14bc5-93f5-495b-830a-1fbf35f80272", "name": "done1"}, {"parameters": {"content": "collect all", "height": 280, "width": 320, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [2760, 1920], "typeVersion": 1, "id": "1545da51-7cd1-4dcb-9ede-5aceab9ca97f", "name": "Sticky Note18"}, {"parameters": {"content": "to call another agent", "height": 280, "width": 320, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [1080, 1040], "typeVersion": 1, "id": "875a7826-cbec-49f0-b3d4-6aedefe3e8ee", "name": "Sticky Note19"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [2180, 2320], "id": "36df8ca4-d7d8-4330-84e2-1f12f2ce18f3", "name": "Merge All Enriched Posts from single post1"}, {"parameters": {"assignments": {"assignments": [{"id": "9561c0b3-fd12-490f-be0b-7d6b287819c7", "name": "content", "value": "={{ $json.content }}", "type": "string"}, {"id": "4d682e28-601b-4f73-afb8-77f782e14f55", "name": "source_type", "value": "={{ $('Code1').item.json.source_type }}", "type": "string"}, {"id": "f263bf56-7d74-491e-82c7-dfd8de9bea65", "name": "source_identifier", "value": "={{ $('Code1').item.json.source_identifier }}", "type": "string"}, {"id": "96df13ec-5215-4d20-9092-187a620eff18", "name": "type", "value": "={{ $('Code1').item.json.type }}", "type": "string"}, {"id": "3356a7d9-2a19-4100-a878-9fcd3e31045b", "name": "post_url", "value": "={{ $('Code1').item.json.post_url }}", "type": "string"}, {"id": "32b4d00f-6235-4d60-a942-83a591bc665e", "name": "media_urls", "value": "={{ $('Code1').item.json.media_urls }}", "type": "array"}, {"id": "a6876711-2a11-4f75-8e59-61f1416b2296", "name": "caption", "value": "={{ $('Code1').item.json.caption }}", "type": "string"}, {"id": "a9f369a8-1d79-4e08-90e1-49fca15a2cd0", "name": "hashtags", "value": "={{ $('Code1').item.json.hashtags }}", "type": "array"}, {"id": "31acd1f2-bc2a-4a27-ab19-98008bf2cc1f", "name": "like_count", "value": "={{ $('Code1').item.json.like_count }}", "type": "number"}, {"id": "23491cf4-2032-453a-a393-30222d8f7c6b", "name": "view_count", "value": "={{ $('Code1').item.json.view_count }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "id": "ca0bee24-101b-4ceb-a30b-05b304cca5f9", "name": "4b. Set: Add Image Analysis1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2260, 1920]}, {"parameters": {"assignments": {"assignments": [{"id": "set_transcript-pes-v3", "name": "video_transcript", "type": "string", "value": "={{ $json.transcript}}"}, {"id": "d52ec436-bb2f-4221-b05d-8925dde29f90", "name": "durationSec", "value": "={{ $json.durationSec }}", "type": "number"}, {"id": "d8826197-fafb-4c95-b586-4e93c2389443", "name": "source_type", "value": "={{ $('Code1').item.json.source_type }}", "type": "string"}, {"id": "d19f1334-3646-457e-8a36-f6067da460b5", "name": "source_identifier", "value": "={{ $('Code1').item.json.source_identifier }}", "type": "string"}, {"id": "44f2896c-f782-4175-a961-59fcad225971", "name": "type", "value": "={{ $('Code1').item.json.type }}", "type": "string"}, {"id": "c935c3ed-1401-4c5f-a6d8-377ba0967694", "name": "post_url", "value": "={{ $('Code1').item.json.post_url }}", "type": "string"}, {"id": "816a03de-a18f-46e9-a775-8822b9c0ab8c", "name": "caption", "value": "={{ $('Code1').item.json.caption }}", "type": "string"}, {"id": "42b94b13-37f6-4e59-9281-c08f7589025f", "name": "hashtags", "value": "={{ $('Code1').item.json.hashtags }}", "type": "array"}, {"id": "61274c43-fb26-4dc1-afe9-f45b4f5a559c", "name": "like_count", "value": "={{ $('Code1').item.json.like_count }}", "type": "number"}, {"id": "6ed8e832-9ced-42fe-a249-1ef6ecd09a89", "name": "view_count", "value": "={{ $('Code1').item.json.view_count }}", "type": "number"}]}, "options": {}}, "id": "1c9789a0-3d61-42de-8bdf-1d6d36ff83bb", "name": "4a. Set: Add Transcript1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2240, 1620]}, {"parameters": {"assignments": {"assignments": [{"id": "set_transcript-pes-v3", "name": "video_transcript", "type": "string", "value": "={{ $json.text }}"}]}, "options": {}}, "id": "9bcfb71e-31b5-42c1-8b37-7ef3c9437b41", "name": "Enriched Carousel Data3", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2160, 1080]}, {"parameters": {"modelId": "gpt-4o", "messages": {"values": [{"content": "You are an expert social media analyst. You will be given the caption, hashtags, and a media analysis (if available) for an Instagram post. Combine all this information to provide a comprehensive summary of the post's content, style, tone, and potential engagement drivers.", "role": "system"}, {"content": "=Post ID: {{ $json.id || $json.shortCode || 'N/A' }}\nCaption: {{ $json.caption || 'No caption' }}\nHashtags: {{ $json.hashtags ? $json.hashtags.join(', ') : 'None' }}\n{{ $json.video_transcript ? 'Video Transcript Summary: ' + $json.video_transcript : '' }}\n{{ $json.image_analysis ? 'Image Analysis: ' + $json.image_analysis : '' }}"}]}, "options": {"temperature": 0.5}}, "id": "7213f8e2-9ff8-42df-ad43-bc57adb474b6", "name": "6. OpenAI: Summarize Full Post3", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [4440, -220], "credentials": {"openAiApi": {"id": "MH8qxTiIzNpDwdbz", "name": "OpenAi account"}}, "continueOnFail": true}, {"parameters": {"content": "Converts JSON String to JSON Array", "height": 600, "width": 300, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [220, 1800], "typeVersion": 1, "id": "7fc41fd9-c6b4-4126-b9ee-70722788ecc1", "name": "Sticky Note20"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolThink", "typeVersion": 1, "position": [-20, 0], "id": "9def2ca4-435a-4651-9b79-392a0f609c41", "name": "Think"}, {"parameters": {"content": "viral finder v0", "height": 2480, "width": 5360}, "type": "n8n-nodes-base.stickyNote", "position": [-1300, 720], "typeVersion": 1, "id": "fa58a95e-31ce-4440-a1d7-7ff4e76a9aee", "name": "Sticky Note21"}, {"parameters": {}, "type": "n8n-nodes-base.stickyNote", "position": [-880, 2320], "typeVersion": 1, "id": "428cc2dc-f65f-461c-befe-2b9b399885e1", "name": "Sticky Note22"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "MCP Client1": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "On form submission": {"main": [[]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "done", "type": "main", "index": 0}], [{"node": "Is Carousel", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Is Carousel": {"main": [[{"node": "determine media type", "type": "main", "index": 0}], [{"node": "Is Video?1", "type": "main", "index": 0}]]}, "OpenAI1": {"main": [[{"node": "4b. Set: Add Image Analysis", "type": "main", "index": 0}]]}, "Is Video?1": {"main": [[{"node": "Transcribe Video", "type": "main", "index": 0}], [{"node": "OpenAI2", "type": "main", "index": 0}]]}, "OpenAI2": {"main": [[{"node": "4b. Set: Add Image Analysis2", "type": "main", "index": 0}]]}, "4a. Set: Add Transcript2": {"main": [[{"node": "Merge All Enriched Posts from single post", "type": "main", "index": 0}]]}, "4b. Set: Add Image Analysis2": {"main": [[{"node": "Merge All Enriched Posts from single post", "type": "main", "index": 0}]]}, "HTTP Request3": {"main": [[{"node": "4a. Set: Add Transcript", "type": "main", "index": 0}]]}, "Loop Over Carousel Media": {"main": [[{"node": "Aggregate Carousel Analyses", "type": "main", "index": 0}], [{"node": "Is Video?", "type": "main", "index": 0}]]}, "determine media type": {"main": [[{"node": "Loop Over Carousel Media", "type": "main", "index": 0}]]}, "Transcribe Video": {"main": [[{"node": "4a. Set: Add Transcript2", "type": "main", "index": 0}]]}, "End of Carousel Media": {"main": [[{"node": "Loop Over Carousel Media", "type": "main", "index": 0}]]}, "Is Video?": {"main": [[{"node": "HTTP Request3", "type": "main", "index": 0}], [{"node": "OpenAI1", "type": "main", "index": 0}]]}, "Aggregate Carousel Analyses": {"main": [[{"node": "Enriched Carousel Data", "type": "main", "index": 0}]]}, "Enriched Carousel Data": {"main": [[{"node": "Merge All Enriched Posts", "type": "main", "index": 0}]]}, "Merge All Enriched Posts": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "6. OpenAI: Summarize Full Post": {"main": [[]]}, "MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Merge All Enriched Posts from single post": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "4b. Set: Add Image Analysis": {"main": [[{"node": "End of Carousel Media", "type": "main", "index": 0}]]}, "4a. Set: Add Transcript": {"main": [[{"node": "End of Carousel Media", "type": "main", "index": 0}]]}, "done": {"main": [[{"node": "Enriched Carousel Data1", "type": "main", "index": 0}]]}, "Enriched Carousel Data1": {"main": [[{"node": "6. OpenAI: Summarize Full Post", "type": "main", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "AI Agent2", "type": "ai_languageModel", "index": 0}]]}, "MCP Client4": {"ai_tool": [[{"node": "AI Agent2", "type": "ai_tool", "index": 0}]]}, "Simple Memory2": {"ai_memory": [[{"node": "AI Agent2", "type": "ai_memory", "index": 0}]]}, "MCP Client5": {"ai_tool": [[{"node": "AI Agent2", "type": "ai_tool", "index": 0}]]}, "AI Agent2": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "6. OpenAI: Summarize Full Post2": {"main": [[{"node": "6. OpenAI: Summarize Full Post3", "type": "main", "index": 0}]]}, "Loop Over Items1": {"main": [[{"node": "done1", "type": "main", "index": 0}], [{"node": "Is Carousel1", "type": "main", "index": 0}]]}, "Code1": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "Is Carousel1": {"main": [[{"node": "determine media type1", "type": "main", "index": 0}], [{"node": "Is Video?2", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "4b. Set: Add Image Analysis1", "type": "main", "index": 0}]]}, "Is Video?2": {"main": [[{"node": "Transcribe Video1", "type": "main", "index": 0}], [{"node": "OpenAI3", "type": "main", "index": 0}]]}, "OpenAI3": {"main": [[{"node": "4b. Set: Add Image Analysis3", "type": "main", "index": 0}]]}, "4a. Set: Add Transcript3": {"main": [[{"node": "Merge All Enriched Posts from single post1", "type": "main", "index": 0}]]}, "4b. Set: Add Image Analysis3": {"main": [[{"node": "Merge All Enriched Posts from single post1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "4a. Set: Add Transcript1", "type": "main", "index": 0}]]}, "Loop Over Carousel Media1": {"main": [[{"node": "Aggregate Carousel Analyses1", "type": "main", "index": 0}], [{"node": "Is Video?3", "type": "main", "index": 0}]]}, "determine media type1": {"main": [[{"node": "Loop Over Carousel Media1", "type": "main", "index": 0}]]}, "Transcribe Video1": {"main": [[{"node": "4a. Set: Add Transcript3", "type": "main", "index": 0}]]}, "End of Carousel Media1": {"main": [[{"node": "Loop Over Carousel Media1", "type": "main", "index": 0}]]}, "Is Video?3": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "OpenAI", "type": "main", "index": 0}]]}, "Aggregate Carousel Analyses1": {"main": [[{"node": "Enriched Carousel Data2", "type": "main", "index": 0}]]}, "Enriched Carousel Data2": {"main": [[{"node": "Merge All Enriched Posts1", "type": "main", "index": 0}]]}, "Merge All Enriched Posts1": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "done1": {"main": [[{"node": "Enriched Carousel Data3", "type": "main", "index": 0}]]}, "Merge All Enriched Posts from single post1": {"main": [[{"node": "Loop Over Items1", "type": "main", "index": 0}]]}, "4b. Set: Add Image Analysis1": {"main": [[{"node": "End of Carousel Media1", "type": "main", "index": 0}]]}, "4a. Set: Add Transcript1": {"main": [[{"node": "End of Carousel Media1", "type": "main", "index": 0}]]}, "Enriched Carousel Data3": {"main": [[{"node": "6. OpenAI: Summarize Full Post2", "type": "main", "index": 0}]]}, "Think": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "ea29c8ba-1fd0-4fb1-ba73-8893ca59dd6a", "meta": {"templateCredsSetupCompleted": true, "instanceId": "c07f651661162007c8c5c43f5e33a7484e639af6bbc95178ede8f7cdd562070d"}, "id": "nEmbvSs7YogeudJ8", "tags": []}