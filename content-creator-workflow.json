{"name": "Content Creator Research & Scripting Assistant", "nodes": [{"id": "manual-trigger", "name": "Start Workflow", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300], "parameters": {}}, {"id": "set-variables", "name": "Set Research Variables", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 300], "parameters": {"assignments": {"assignments": [{"id": "topic", "name": "topic", "value": "{{ $json.topic || 'artificial intelligence' }}", "type": "string"}, {"id": "content_type", "name": "content_type", "value": "{{ $json.content_type || 'youtube video' }}", "type": "string"}, {"id": "target_audience", "name": "target_audience", "value": "{{ $json.target_audience || 'general audience' }}", "type": "string"}, {"id": "research_depth", "name": "research_depth", "value": "{{ $json.research_depth || 'medium' }}", "type": "string"}]}}}, {"id": "reddit-research", "name": "Reddit Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 180], "parameters": {"url": "https://www.reddit.com/search.json?q={{ $node['Set Research Variables'].json.topic }}&sort=hot&limit=10", "method": "GET", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "n8n-content-creator-bot/1.0"}]}}}, {"id": "news-research", "name": "News Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 300], "parameters": {"url": "https://newsapi.org/v2/everything?q={{ $node['Set Research Variables'].json.topic }}&sortBy=publishedAt&pageSize=10&apiKey=YOUR_NEWS_API_KEY", "method": "GET"}}, {"id": "trends-research", "name": "Google Trends Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 420], "parameters": {"url": "https://serpapi.com/search.json?engine=google_trends&q={{ $node['Set Research Variables'].json.topic }}&api_key=YOUR_SERPAPI_KEY", "method": "GET"}}, {"id": "merge-research", "name": "Merge Research Data", "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [900, 300], "parameters": {"mode": "combine", "combineBy": "combineAll"}}, {"id": "process-research", "name": "Process Research with AI", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1120, 300], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are a content research assistant. Analyze the provided research data and create a comprehensive summary focusing on key insights, trending topics, and interesting angles for content creation."}, {"role": "user", "content": "Topic: {{ $node['Set Research Variables'].json.topic }}\nContent Type: {{ $node['Set Research Variables'].json.content_type }}\nTarget Audience: {{ $node['Set Research Variables'].json.target_audience }}\n\nResearch Data:\nReddit: {{ $node['Reddit Research'].json }}\nNews: {{ $node['News Research'].json }}\nTrends: {{ $node['Google Trends Research'].json }}\n\nPlease provide:\n1. Key insights and trends\n2. Interesting angles for content\n3. Current discussions and debates\n4. Relevant statistics or data points\n5. Potential content hooks"}]}, "maxTokens": 1000, "temperature": 0.7}}, {"id": "generate-script", "name": "Generate Script Outline", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1340, 180], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are a professional script writer. Create engaging script outlines for content creators based on research data."}, {"role": "user", "content": "Based on this research summary: {{ $node['Process Research with AI'].json.choices[0].message.content }}\n\nCreate a detailed script outline for a {{ $node['Set Research Variables'].json.content_type }} about {{ $node['Set Research Variables'].json.topic }} targeting {{ $node['Set Research Variables'].json.target_audience }}.\n\nInclude:\n1. Hook/Opening (first 15 seconds)\n2. Main sections with key points\n3. Transitions between sections\n4. Call-to-action\n5. Estimated timing for each section\n6. Suggested visuals or examples"}]}, "maxTokens": 1200, "temperature": 0.8}}, {"id": "generate-ideas", "name": "Generate Content Ideas", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1340, 300], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are a creative content strategist. Generate multiple content ideas and angles based on research data."}, {"role": "user", "content": "Research Summary: {{ $node['Process Research with AI'].json.choices[0].message.content }}\n\nGenerate 10 different content ideas related to {{ $node['Set Research Variables'].json.topic }} for {{ $node['Set Research Variables'].json.content_type }}.\n\nFor each idea provide:\n1. Title/Headline\n2. Brief description\n3. Target audience appeal\n4. Unique angle or hook\n5. Potential engagement factors\n6. Difficulty level to create\n7. Trending potential"}]}, "maxTokens": 1000, "temperature": 0.9}}, {"id": "fact-check", "name": "Fact Check & Verify", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1340, 420], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are a fact-checking assistant. Analyze content for accuracy and provide verification guidance."}, {"role": "user", "content": "Research Data: {{ $node['Process Research with AI'].json.choices[0].message.content }}\n\nPlease:\n1. Identify key claims and statistics mentioned\n2. Flag any information that needs verification\n3. Suggest reliable sources for fact-checking\n4. Highlight any potential misinformation or outdated data\n5. Provide confidence levels for different claims\n6. Recommend additional research areas if needed"}]}, "maxTokens": 800, "temperature": 0.3}}, {"id": "compile-content", "name": "Compile Final Content", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1560, 300], "parameters": {"assignments": {"assignments": [{"id": "topic", "name": "topic", "value": "{{ $node['Set Research Variables'].json.topic }}", "type": "string"}, {"id": "content_type", "name": "content_type", "value": "{{ $node['Set Research Variables'].json.content_type }}", "type": "string"}, {"id": "target_audience", "name": "target_audience", "value": "{{ $node['Set Research Variables'].json.target_audience }}", "type": "string"}, {"id": "research_summary", "name": "research_summary", "value": "{{ $node['Process Research with AI'].json.choices[0].message.content }}", "type": "string"}, {"id": "script_outline", "name": "script_outline", "value": "{{ $node['Generate Script Outline'].json.choices[0].message.content }}", "type": "string"}, {"id": "content_ideas", "name": "content_ideas", "value": "{{ $node['Generate Content Ideas'].json.choices[0].message.content }}", "type": "string"}, {"id": "fact_check", "name": "fact_check", "value": "{{ $node['Fact Check & Verify'].json.choices[0].message.content }}", "type": "string"}, {"id": "created_date", "name": "created_date", "value": "{{ $now }}", "type": "string"}]}}}, {"id": "save-to-sheets", "name": "Save to Google Sheets", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1780, 240], "parameters": {"operation": "append", "documentId": "YOUR_GOOGLE_SHEET_ID", "sheetName": "Content Research", "range": "A:H", "options": {"valueInputOption": "USER_ENTERED"}, "columns": {"mappingMode": "defineBelow", "value": {"Topic": "={{ $json.topic }}", "Content Type": "={{ $json.content_type }}", "Target Audience": "={{ $json.target_audience }}", "Research Summary": "={{ $json.research_summary }}", "Script Outline": "={{ $json.script_outline }}", "Content Ideas": "={{ $json.content_ideas }}", "Fact Check": "={{ $json.fact_check }}", "Created Date": "={{ $json.created_date }}"}}}}, {"id": "send-email", "name": "Send Email Summary", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1780, 360], "parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "Content Research Complete: {{ $json.topic }}", "message": "Content research and script generation completed for: {{ $json.topic }}\n\nContent Type: {{ $json.content_type }}\nTarget Audience: {{ $json.target_audience }}\n\n=== RESEARCH SUMMARY ===\n{{ $json.research_summary }}\n\n=== SCRIPT OUTLINE ===\n{{ $json.script_outline }}\n\n=== CONTENT IDEAS ===\n{{ $json.content_ideas }}\n\n=== FACT CHECK NOTES ===\n{{ $json.fact_check }}\n\nGenerated on: {{ $json.created_date }}", "options": {}}}], "connections": {"Start Workflow": {"main": [[{"node": "Set Research Variables", "type": "main", "index": 0}]]}, "Set Research Variables": {"main": [[{"node": "Reddit Research", "type": "main", "index": 0}, {"node": "News Research", "type": "main", "index": 0}, {"node": "Google Trends Research", "type": "main", "index": 0}]]}, "Reddit Research": {"main": [[{"node": "Merge Research Data", "type": "main", "index": 0}]]}, "News Research": {"main": [[{"node": "Merge Research Data", "type": "main", "index": 1}]]}, "Google Trends Research": {"main": [[{"node": "Merge Research Data", "type": "main", "index": 2}]]}, "Merge Research Data": {"main": [[{"node": "Process Research with AI", "type": "main", "index": 0}]]}, "Process Research with AI": {"main": [[{"node": "Generate Script Outline", "type": "main", "index": 0}, {"node": "Generate Content Ideas", "type": "main", "index": 0}, {"node": "Fact Check & Verify", "type": "main", "index": 0}]]}, "Generate Script Outline": {"main": [[{"node": "Compile Final Content", "type": "main", "index": 0}]]}, "Generate Content Ideas": {"main": [[{"node": "Compile Final Content", "type": "main", "index": 1}]]}, "Fact Check & Verify": {"main": [[{"node": "Compile Final Content", "type": "main", "index": 2}]]}, "Compile Final Content": {"main": [[{"node": "Save to Google Sheets", "type": "main", "index": 0}, {"node": "Send Email Summary", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}