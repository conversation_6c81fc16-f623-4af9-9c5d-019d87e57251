<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Content Research Studio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-5xl font-bold text-white mb-4">
                AI Content Research Studio
            </h1>
            <p class="text-xl text-white/80 max-w-2xl mx-auto">
                Generate viral content ideas powered by YouTube, Twitter, and Reddit research
            </p>
        </div>

        <!-- Main Form -->
        <div class="max-w-2xl mx-auto glass-effect rounded-2xl p-8 shadow-2xl">
            <form id="contentForm" class="space-y-6">
                <div>
                    <label class="block text-white font-semibold mb-2">
                        🎯 What topic do you want to research?
                    </label>
                    <input 
                        type="text" 
                        id="topic"
                        placeholder="e.g., AI marketing automation, social media tools"
                        class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                        required
                    >
                </div>

                <div>
                    <label class="block text-white font-semibold mb-2">
                        📱 Content Format
                    </label>
                    <select 
                        id="contentType"
                        class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white focus:outline-none focus:ring-2 focus:ring-white/50"
                    >
                        <option value="reel">Instagram Reel (30-45s)</option>
                        <option value="linkedin">LinkedIn Post</option>
                        <option value="thread">Twitter Thread</option>
                        <option value="blog">Blog Post Outline</option>
                    </select>
                </div>

                <div>
                    <label class="block text-white font-semibold mb-2">
                        ⏰ When do you need this?
                    </label>
                    <select 
                        id="urgency"
                        class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white focus:outline-none focus:ring-2 focus:ring-white/50"
                    >
                        <option value="urgent">ASAP (2 hours)</option>
                        <option value="today">End of day</option>
                        <option value="week">This week</option>
                    </select>
                </div>

                <div>
                    <label class="block text-white font-semibold mb-2">
                        👥 Target Audience (Optional)
                    </label>
                    <input 
                        type="text" 
                        id="targetAudience"
                        placeholder="e.g., Marketing managers, Small business owners"
                        class="w-full px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50"
                    >
                </div>

                <button 
                    type="submit"
                    class="w-full py-4 bg-white text-purple-700 font-bold rounded-lg hover:bg-white/90 transition-all duration-200 text-lg shadow-lg"
                >
                    🚀 Generate Content Ideas
                </button>
            </form>

            <!-- Status Display -->
            <div id="status" class="mt-6 text-center hidden">
                <div class="inline-flex items-center px-4 py-2 bg-yellow-500/20 text-yellow-200 rounded-lg">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-200 mr-2"></div>
                    Processing your request...
                </div>
            </div>

            <!-- Success Message -->
            <div id="success" class="mt-6 text-center hidden">
                <div class="bg-green-500/20 text-green-200 p-4 rounded-lg">
                    ✅ Content ideas generated! Check your email or Google Docs for results.
                </div>
            </div>
        </div>

        <!-- Features -->
        <div class="max-w-4xl mx-auto mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center text-white">
                <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    🔍
                </div>
                <h3 class="text-xl font-semibold mb-2">Multi-Platform Research</h3>
                <p class="text-white/80">Analyzes YouTube, Twitter, and Reddit for trending content</p>
            </div>
            <div class="text-center text-white">
                <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    🤖
                </div>
                <h3 class="text-xl font-semibold mb-2">AI-Powered Generation</h3>
                <p class="text-white/80">Creates platform-specific content with hooks and CTAs</p>
            </div>
            <div class="text-center text-white">
                <div class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    ⚡
                </div>
                <h3 class="text-xl font-semibold mb-2">Fast Delivery</h3>
                <p class="text-white/80">Get comprehensive content packages in minutes</p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('contentForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                topic: document.getElementById('topic').value,
                contentType: document.getElementById('contentType').value,
                urgency: document.getElementById('urgency').value,
                targetAudience: document.getElementById('targetAudience').value
            };

            // Show loading state
            document.getElementById('status').classList.remove('hidden');
            document.getElementById('success').classList.add('hidden');

            try {
                // Replace with your n8n webhook URL
                const response = await fetch('YOUR_N8N_WEBHOOK_URL', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                if (response.ok) {
                    document.getElementById('status').classList.add('hidden');
                    document.getElementById('success').classList.remove('hidden');
                    document.getElementById('contentForm').reset();
                } else {
                    throw new Error('Request failed');
                }
            } catch (error) {
                document.getElementById('status').classList.add('hidden');
                alert('Something went wrong. Please try again.');
            }
        });
    </script>
</body>
</html>
