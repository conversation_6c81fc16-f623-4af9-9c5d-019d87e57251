'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface ContentRequest {
  topic: string;
  contentType: string;
  urgency: string;
  targetAudience: string;
}

interface ContentResult {
  id: string;
  status: 'processing' | 'completed' | 'error';
  request: ContentRequest;
  results?: {
    instagramContent: string;
    linkedinContent: string;
    twitterContent: string;
    researchSummary: string;
  };
  createdAt: Date;
}

const ContentResearchDashboard: React.FC = () => {
  const [request, setRequest] = useState<ContentRequest>({
    topic: '',
    contentType: 'reel',
    urgency: 'today',
    targetAudience: ''
  });
  
  const [results, setResults] = useState<ContentResult[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);

    const newResult: ContentResult = {
      id: Date.now().toString(),
      status: 'processing',
      request: { ...request },
      createdAt: new Date()
    };

    setResults(prev => [newResult, ...prev]);

    // Call your n8n webhook
    try {
      const response = await fetch('YOUR_N8N_WEBHOOK_URL', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request)
      });

      if (response.ok) {
        const data = await response.json();
        setResults(prev => prev.map(r => 
          r.id === newResult.id 
            ? { ...r, status: 'completed', results: data }
            : r
        ));
      }
    } catch (error) {
      setResults(prev => prev.map(r => 
        r.id === newResult.id 
          ? { ...r, status: 'error' }
          : r
      ));
    }

    setIsProcessing(false);
    setRequest({ topic: '', contentType: 'reel', urgency: 'today', targetAudience: '' });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent mb-2">
            AI Content Research Studio
          </h1>
          <p className="text-gray-400">Generate viral content ideas powered by multi-platform research</p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Request Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700"
          >
            <h2 className="text-2xl font-semibold mb-6 flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
              New Research Request
            </h2>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">Research Topic</label>
                <input
                  type="text"
                  value={request.topic}
                  onChange={(e) => setRequest(prev => ({ ...prev, topic: e.target.value }))}
                  placeholder="e.g., AI marketing automation, social media tools"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Content Format</label>
                <select
                  value={request.contentType}
                  onChange={(e) => setRequest(prev => ({ ...prev, contentType: e.target.value }))}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="reel">Instagram Reel (30-45s)</option>
                  <option value="linkedin">LinkedIn Post</option>
                  <option value="thread">Twitter Thread</option>
                  <option value="blog">Blog Post Outline</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Timeline</label>
                <select
                  value={request.urgency}
                  onChange={(e) => setRequest(prev => ({ ...prev, urgency: e.target.value }))}
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value="urgent">ASAP (2 hours)</option>
                  <option value="today">End of day</option>
                  <option value="week">This week</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Target Audience</label>
                <input
                  type="text"
                  value={request.targetAudience}
                  onChange={(e) => setRequest(prev => ({ ...prev, targetAudience: e.target.value }))}
                  placeholder="e.g., Marketing managers, Small business owners"
                  className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <button
                type="submit"
                disabled={isProcessing}
                className="w-full py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  'Generate Content Ideas'
                )}
              </button>
            </form>
          </motion.div>

          {/* Results Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700"
          >
            <h2 className="text-2xl font-semibold mb-6 flex items-center">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
              Recent Results
            </h2>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {results.length === 0 ? (
                <div className="text-center text-gray-400 py-8">
                  <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    📊
                  </div>
                  <p>No content generated yet</p>
                  <p className="text-sm">Submit a request to get started</p>
                </div>
              ) : (
                results.map((result) => (
                  <motion.div
                    key={result.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gray-700/30 rounded-lg p-4 border border-gray-600"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium truncate">{result.request.topic}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        result.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                        result.status === 'processing' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-red-500/20 text-red-400'
                      }`}>
                        {result.status}
                      </span>
                    </div>
                    <div className="text-sm text-gray-400 mb-2">
                      {result.request.contentType} • {result.createdAt.toLocaleTimeString()}
                    </div>
                    {result.status === 'completed' && result.results && (
                      <button className="text-blue-400 hover:text-blue-300 text-sm">
                        View Results →
                      </button>
                    )}
                  </motion.div>
                ))
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ContentResearchDashboard;
