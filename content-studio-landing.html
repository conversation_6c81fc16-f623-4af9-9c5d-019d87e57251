<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Content Studio - Generate Viral Content Ideas in Minutes</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .glass { background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); }
        .glow { box-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }
        .animate-float { animation: float 6s ease-in-out infinite; }
        @keyframes float { 0%, 100% { transform: translateY(0px); } 50% { transform: translateY(-20px); } }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <!-- Hero Section -->
    <section class="gradient-bg min-h-screen flex items-center relative overflow-hidden">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full animate-float"></div>
            <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-white/5 rounded-full animate-float" style="animation-delay: -3s;"></div>
        </div>

        <div class="container mx-auto px-6 relative z-10">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                    Generate <span class="text-yellow-300">Viral Content</span><br>
                    Ideas in <span class="text-yellow-300">Minutes</span>
                </h1>
                <p class="text-xl md:text-2xl mb-8 text-white/90 max-w-3xl mx-auto">
                    AI-powered content research that analyzes YouTube, Twitter, and Reddit to create 
                    platform-specific content that actually converts
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                    <a href="#request-form" class="bg-white text-purple-700 px-8 py-4 rounded-full font-semibold text-lg hover:bg-white/90 transition-all glow">
                        🚀 Start Free Research
                    </a>
                    <a href="#how-it-works" class="glass px-8 py-4 rounded-full font-semibold text-lg hover:bg-white/20 transition-all">
                        📖 See How It Works
                    </a>
                </div>
                
                <!-- Social Proof -->
                <div class="flex flex-wrap justify-center items-center gap-8 text-white/70">
                    <div class="flex items-center gap-2">
                        <span class="text-yellow-300">⭐⭐⭐⭐⭐</span>
                        <span>4.9/5 Rating</span>
                    </div>
                    <div>📊 500+ Content Pieces Generated</div>
                    <div>⚡ 2-Minute Average Delivery</div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section id="how-it-works" class="py-20 bg-gray-900">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center mb-16">How It Works</h2>
            <div class="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl">
                        🔍
                    </div>
                    <h3 class="text-xl font-semibold mb-4">1. Multi-Platform Research</h3>
                    <p class="text-gray-400">Our AI analyzes trending content across YouTube, Twitter, and Reddit to find what's actually working in your niche</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl">
                        🤖
                    </div>
                    <h3 class="text-xl font-semibold mb-4">2. AI Content Generation</h3>
                    <p class="text-gray-400">Advanced AI creates platform-specific content with hooks, scripts, and CTAs tailored to your audience</p>
                </div>
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-r from-pink-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6 text-2xl">
                        📱
                    </div>
                    <h3 class="text-xl font-semibold mb-4">3. Ready-to-Use Content</h3>
                    <p class="text-gray-400">Get Instagram reels, LinkedIn posts, Twitter threads, and research summaries delivered instantly</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Request Form -->
    <section id="request-form" class="py-20 bg-gray-800">
        <div class="container mx-auto px-6">
            <div class="max-w-2xl mx-auto">
                <h2 class="text-4xl font-bold text-center mb-4">Get Your Content Package</h2>
                <p class="text-center text-gray-400 mb-12">Tell us what you want to research and we'll generate viral content ideas for you</p>
                
                <div x-data="contentForm()" class="glass rounded-2xl p-8">
                    <form @submit.prevent="submitForm" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium mb-2">🎯 Research Topic *</label>
                            <input 
                                type="text" 
                                x-model="form.topic"
                                placeholder="e.g., AI marketing automation, social media tools, content creation"
                                class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-white placeholder-gray-400"
                                required
                            >
                        </div>

                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">📱 Content Type</label>
                                <select 
                                    x-model="form.contentType"
                                    class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 text-white"
                                >
                                    <option value="reel">Instagram Reel (30-45s)</option>
                                    <option value="linkedin">LinkedIn Post</option>
                                    <option value="thread">Twitter Thread</option>
                                    <option value="blog">Blog Post Outline</option>
                                    <option value="all">All Formats</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">⏰ Timeline</label>
                                <select 
                                    x-model="form.urgency"
                                    class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 text-white"
                                >
                                    <option value="standard">Standard (2-3 hours)</option>
                                    <option value="urgent">Rush (30 minutes) +$50</option>
                                    <option value="today">Same day</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">👥 Target Audience</label>
                            <input 
                                type="text" 
                                x-model="form.audience"
                                placeholder="e.g., Marketing managers, Small business owners, Content creators"
                                class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 text-white placeholder-gray-400"
                            >
                        </div>

                        <div>
                            <label class="block text-sm font-medium mb-2">📧 Email for Results *</label>
                            <input 
                                type="email" 
                                x-model="form.email"
                                placeholder="<EMAIL>"
                                class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 text-white placeholder-gray-400"
                                required
                            >
                        </div>

                        <div class="bg-gray-700/30 rounded-lg p-4">
                            <h4 class="font-semibold mb-2">📦 What You'll Get:</h4>
                            <ul class="text-sm text-gray-300 space-y-1">
                                <li>✅ Platform-specific content scripts</li>
                                <li>✅ Viral hooks and CTAs</li>
                                <li>✅ Visual cues for video editing</li>
                                <li>✅ Research summary with sources</li>
                                <li>✅ Hashtag recommendations</li>
                            </ul>
                        </div>

                        <button 
                            type="submit"
                            :disabled="loading"
                            class="w-full py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 rounded-lg font-semibold text-lg transition-all duration-200 flex items-center justify-center"
                        >
                            <span x-show="!loading">🚀 Generate My Content Package</span>
                            <span x-show="loading" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Processing...
                            </span>
                        </button>
                    </form>

                    <!-- Success Message -->
                    <div x-show="success" class="mt-6 p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-300">
                        <h4 class="font-semibold mb-2">🎉 Request Submitted Successfully!</h4>
                        <p class="text-sm">Your content package is being generated. You'll receive an email with your results shortly.</p>
                    </div>

                    <!-- Error Message -->
                    <div x-show="error" class="mt-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300">
                        <h4 class="font-semibold mb-2">❌ Something went wrong</h4>
                        <p class="text-sm">Please try again or contact support if the problem persists.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section class="py-20 bg-gray-900">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center mb-16">Why Choose AI Content Studio?</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-4xl mb-4">⚡</div>
                    <h3 class="text-lg font-semibold mb-2">Lightning Fast</h3>
                    <p class="text-gray-400 text-sm">Get results in 2-3 minutes, not hours</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-lg font-semibold mb-2">Highly Targeted</h3>
                    <p class="text-gray-400 text-sm">Content tailored to your specific audience</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">📊</div>
                    <h3 class="text-lg font-semibold mb-2">Data-Driven</h3>
                    <p class="text-gray-400 text-sm">Based on real trending content analysis</p>
                </div>
                <div class="text-center">
                    <div class="text-4xl mb-4">🔄</div>
                    <h3 class="text-lg font-semibold mb-2">Multi-Platform</h3>
                    <p class="text-gray-400 text-sm">Optimized for Instagram, LinkedIn, Twitter</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-400">&copy; 2024 AI Content Studio. Powered by advanced AI research.</p>
            <p class="text-sm text-gray-500 mt-2">Generate viral content ideas • Save hours of research • Boost engagement</p>
        </div>
    </footer>

    <script>
        function contentForm() {
            return {
                form: {
                    topic: '',
                    contentType: 'all',
                    urgency: 'standard',
                    audience: '',
                    email: ''
                },
                loading: false,
                success: false,
                error: false,

                async submitForm() {
                    this.loading = true;
                    this.success = false;
                    this.error = false;

                    try {
                        // Replace with your n8n webhook URL
                        const response = await fetch('YOUR_N8N_WEBHOOK_URL_HERE', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                chatInput: this.form.topic,
                                contentType: this.form.contentType,
                                urgency: this.form.urgency,
                                targetAudience: this.form.audience,
                                email: this.form.email,
                                timestamp: new Date().toISOString()
                            })
                        });

                        if (response.ok) {
                            this.success = true;
                            this.form = {
                                topic: '',
                                contentType: 'all',
                                urgency: 'standard',
                                audience: '',
                                email: ''
                            };
                        } else {
                            throw new Error('Request failed');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        this.error = true;
                    } finally {
                        this.loading = false;
                    }
                }
            }
        }
    </script>
</body>
</html>
