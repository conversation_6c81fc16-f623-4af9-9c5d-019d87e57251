{"name": "Enhanced Content Orchestrator System", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-400, 200], "id": "main-trigger", "name": "Content Request Trigger", "webhookId": "content-orchestrator-main"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {"temperature": 0.3, "maxTokens": 2000}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-200, 400], "id": "orchestrator-model", "name": "Master Orchestrator Model", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [0, 400], "id": "shared-memory", "name": "Shared Context Memory"}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"tools\",\"name of the tool\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [200, 300], "id": "research-tool", "name": "Research Tool", "credentials": {"mcpClientApi": {"id": "VZrolHwLFQcQ8BY1", "name": "exa"}}}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"Tools\",\"name of the tools\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [200, 400], "id": "scraping-tool", "name": "Web Scraping Tool", "credentials": {"mcpClientApi": {"id": "R1W9MSewn63K3ugI", "name": "playwright"}}}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"tools\",\"name of the tool\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [200, 500], "id": "social-tool", "name": "Social Media Tool", "credentials": {"mcpClientApi": {"id": "S4jTEIdHfBSdiiin", "name": "MCP Client (STDIO) account 2"}}}, {"parameters": {"workflowId": "{{ $vars.RESEARCH_AGENT_WORKFLOW_ID }}", "fields": {"values": [{"name": "topic", "value": "={{ $fromAI('research_topic', 'Topic to research') }}"}, {"name": "research_depth", "value": "={{ $fromAI('research_depth', 'comprehensive') }}"}]}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.1, "position": [400, 300], "id": "research-agent-tool", "name": "Research Agent Tool"}, {"parameters": {"workflowId": "{{ $vars.CONTENT_IDEA_WORKFLOW_ID }}", "fields": {"values": [{"name": "research_data", "value": "={{ $fromAI('research_insights', 'Research insights to transform into content ideas') }}"}, {"name": "content_focus", "value": "={{ $fromAI('content_focus', 'viral engagement') }}"}]}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.1, "position": [400, 400], "id": "content-idea-tool", "name": "Content Idea Generator Tool"}, {"parameters": {"workflowId": "{{ $vars.INSTAGRAM_WORKFLOW_ID }}", "fields": {"values": [{"name": "content_ideas", "value": "={{ $fromAI('instagram_concepts', 'Content concepts for Instagram optimization') }}"}, {"name": "target_audience", "value": "={{ $fromAI('target_audience', 'Target audience for content') }}"}]}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.1, "position": [400, 500], "id": "instagram-tool", "name": "Instagram Specialist Tool"}, {"parameters": {"workflowId": "{{ $vars.LINKEDIN_WORKFLOW_ID }}", "fields": {"values": [{"name": "content_ideas", "value": "={{ $fromAI('linkedin_concepts', 'Content concepts for LinkedIn optimization') }}"}, {"name": "professional_focus", "value": "={{ $fromAI('professional_angle', 'Professional angle for B2B content') }}"}]}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.1, "position": [400, 600], "id": "linkedin-tool", "name": "LinkedIn Specialist <PERSON>l"}, {"parameters": {"workflowId": "{{ $vars.TWITTER_WORKFLOW_ID }}", "fields": {"values": [{"name": "content_ideas", "value": "={{ $fromAI('twitter_concepts', 'Content concepts for Twitter optimization') }}"}, {"name": "viral_focus", "value": "={{ $fromAI('viral_elements', 'Viral elements to emphasize') }}"}]}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.1, "position": [400, 700], "id": "twitter-tool", "name": "Twitter Specialist <PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "You are the Master Content Orchestrator, an intelligent system that coordinates specialized AI agents to create comprehensive content packages. You have access to multiple research tools and can delegate tasks to specialized content creation agents.\n\n## YOUR ROLE\n\nYou are the central intelligence that:\n1. **Analyzes user requests** and determines the optimal research and content strategy\n2. **Coordinates multiple agents** through tool calls to specialized workflows\n3. **Maintains context** across all interactions and agent outputs\n4. **Synthesizes results** into cohesive, actionable content packages\n5. **Ensures quality** by reviewing and refining outputs from specialist agents\n\n## AVAILABLE TOOLS\n\n**Research Tools:**\n- **web_search_exa_exa**: General web research and trending content analysis\n- **company_research_exa_exa**: Company and competitor research\n- **linkedin_search_exa_exa**: LinkedIn-specific content research\n- **crawling_exa_exa**: Deep website analysis and content extraction\n- **playwright_navigate_playwright**: Advanced web scraping and interaction\n\n**Specialist Agent Tools:**\n- **research_agent_tool**: Calls the enhanced research agent for comprehensive analysis\n- **content_idea_tool**: Generates creative viral content concepts\n- **instagram_specialist_tool**: Creates Instagram-optimized content\n- **linkedin_specialist_tool**: Develops professional LinkedIn content\n- **twitter_specialist_tool**: Produces viral Twitter content\n\n## ORCHESTRATION WORKFLOW\n\nFor every user request, follow this intelligent workflow:\n\n### Phase 1: Analysis & Planning\n1. **Analyze the request** - Understand topic, audience, goals, and scope\n2. **Determine research strategy** - Choose appropriate research tools based on query type\n3. **Plan agent coordination** - Decide which specialist agents to engage and in what order\n\n### Phase 2: Research Execution\n1. **Conduct comprehensive research** using multiple tools:\n   - Use web_search_exa_exa for trending content and general insights\n   - Use company_research_exa_exa if business/competitor analysis is needed\n   - Use linkedin_search_exa_exa for professional content research\n   - Use crawling_exa_exa for specific website analysis\n2. **Synthesize research findings** into actionable insights\n\n### Phase 3: Content Creation Coordination\n1. **Call research_agent_tool** with synthesized research data\n2. **Call content_idea_tool** with research insights to generate creative concepts\n3. **Coordinate specialist agents** based on user needs:\n   - Call instagram_specialist_tool for Instagram content\n   - Call linkedin_specialist_tool for professional content\n   - Call twitter_specialist_tool for Twitter content\n\n### Phase 4: Quality Assurance & Delivery\n1. **Review all agent outputs** for consistency and quality\n2. **Synthesize into comprehensive package** with clear organization\n3. **Provide implementation guidance** and next steps\n\n## OUTPUT STRUCTURE\n\nAlways structure your final response as:\n\n🎯 **ORCHESTRATION SUMMARY**\n- Research strategy executed\n- Agents coordinated and their roles\n- Key insights discovered\n- Content package overview\n\n📊 **RESEARCH INSIGHTS**\n- Trending patterns identified\n- Audience behavior insights\n- Competitive landscape analysis\n- Content opportunities discovered\n\n💡 **CONTENT PACKAGE**\n- Instagram content (if requested)\n- LinkedIn content (if requested)\n- Twitter content (if requested)\n- Cross-platform optimization notes\n\n🚀 **IMPLEMENTATION ROADMAP**\n- Posting schedule recommendations\n- Performance tracking suggestions\n- Optimization strategies\n- Next steps for scaling\n\n## ORCHESTRATION PRINCIPLES\n\n1. **Be Intelligent**: Choose tools based on query analysis, not default patterns\n2. **Be Comprehensive**: Use multiple research sources for robust insights\n3. **Be Coordinated**: Ensure all specialist agents work with consistent context\n4. **Be Quality-Focused**: Review and refine all outputs before delivery\n5. **Be Strategic**: Provide actionable guidance beyond just content creation\n\n## EXAMPLE COORDINATION\n\nUser: \"Research AI productivity tools and create content for LinkedIn and Instagram\"\n\nYour approach:\n1. Use web_search_exa_exa to find trending AI productivity tools\n2. Use company_research_exa_exa to analyze key players in the space\n3. Use linkedin_search_exa_exa to understand professional discussions\n4. Call research_agent_tool with comprehensive research data\n5. Call content_idea_tool to generate creative concepts\n6. Call linkedin_specialist_tool for professional content\n7. Call instagram_specialist_tool for visual content\n8. Synthesize everything into a cohesive package with implementation guidance\n\nRemember: You are the conductor of this content creation orchestra. Coordinate intelligently, maintain context across all interactions, and deliver exceptional results that exceed user expectations.", "options": {"systemMessage": "You are the Master Content Orchestrator. Coordinate research tools and specialist agents to create comprehensive content packages."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [-200, 200], "id": "master-orchestrator", "name": "Master Content Orchestrator"}], "pinData": {}, "connections": {"Content Request Trigger": {"main": [[{"node": "Master Content Orchestrator", "type": "main", "index": 0}]]}, "Master Orchestrator Model": {"ai_languageModel": [[{"node": "Master Content Orchestrator", "type": "ai_languageModel", "index": 0}]]}, "Shared Context Memory": {"ai_memory": [[{"node": "Master Content Orchestrator", "type": "ai_memory", "index": 0}]]}, "Research Tool": {"ai_tool": [[{"node": "Master Content Orchestrator", "type": "ai_tool", "index": 0}]]}, "Web Scraping Tool": {"ai_tool": [[{"node": "Master Content Orchestrator", "type": "ai_tool", "index": 0}]]}, "Social Media Tool": {"ai_tool": [[{"node": "Master Content Orchestrator", "type": "ai_tool", "index": 0}]]}, "Research Agent Tool": {"ai_tool": [[{"node": "Master Content Orchestrator", "type": "ai_tool", "index": 0}]]}, "Content Idea Generator Tool": {"ai_tool": [[{"node": "Master Content Orchestrator", "type": "ai_tool", "index": 0}]]}, "Instagram Specialist Tool": {"ai_tool": [[{"node": "Master Content Orchestrator", "type": "ai_tool", "index": 0}]]}, "LinkedIn Specialist Tool": {"ai_tool": [[{"node": "Master Content Orchestrator", "type": "ai_tool", "index": 0}]]}, "Twitter Specialist Tool": {"ai_tool": [[{"node": "Master Content Orchestrator", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "enhanced-orchestrator-v1", "meta": {"templateCredsSetupCompleted": true}, "id": "EnhancedContentOrchestrator", "tags": ["orchestrator", "master", "content"]}