<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Content Research Studio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0a;
        }

        /* Brand Colors */
        .brand-gradient {
            background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 50%, #F59E0B 100%);
        }
        .brand-purple { background: #8B5CF6; }
        .brand-card {
            background: rgba(17, 24, 39, 0.8);
            border: 1px solid rgba(139, 92, 246, 0.2);
            backdrop-filter: blur(10px);
        }

        /* Minimal Progress Animation */
        .progress-line {
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(90deg, #8B5CF6, #EC4899);
        }

        /* Subtle Pulse */
        .pulse-minimal { animation: pulseMinimal 3s ease-in-out infinite; }
        @keyframes pulseMinimal {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        /* Clean Slide Animation */
        .slide-up {
            animation: slideUp 0.5s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }
        @keyframes slideUp {
            to { opacity: 1; transform: translateY(0); }
        }

        /* Professional Button */
        .btn-primary {
            background: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);
        }

        /* Minimal Status Indicator */
        .status-dot {
            width: 8px;
            height: 8px;
            background: #10B981;
            border-radius: 50%;
            animation: statusPulse 2s infinite;
        }
        @keyframes statusPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.4; }
        }
    </style>
</head>
<body class="text-white min-h-screen">
    <div x-data="workflowApp()" class="min-h-screen">
        <!-- Professional Header -->
        <header class="border-b border-gray-800 bg-black/50 backdrop-blur-sm">
            <div class="container mx-auto px-6 py-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 brand-gradient rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">AI</span>
                        </div>
                        <h1 class="text-xl font-semibold">Content Research Studio</h1>
                    </div>
                    <div class="text-sm text-gray-400">Powered by AI Automation</div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="container mx-auto px-6 py-12">
            <!-- Input Form (Show when not processing) -->
            <div x-show="!isProcessing && !showResults" class="max-w-2xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold mb-4">Scale Your Content With<br>AI Research & Generation</h2>
                    <p class="text-gray-400 text-lg">Generate platform-specific content ideas backed by real-time research from YouTube, Twitter, and Reddit.</p>
                </div>

                <div class="brand-card rounded-2xl p-8">
                    <form @submit.prevent="startWorkflow" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium mb-3 text-gray-300">Research Topic</label>
                            <input
                                type="text"
                                x-model="form.topic"
                                placeholder="e.g., AI marketing automation, social media tools"
                                class="w-full px-4 py-4 bg-gray-900/50 border border-gray-700 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white placeholder-gray-500 transition-all"
                                required
                            >
                        </div>
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium mb-3 text-gray-300">Content Format</label>
                                <select x-model="form.contentType" class="w-full px-4 py-4 bg-gray-900/50 border border-gray-700 rounded-xl focus:ring-2 focus:ring-purple-500 text-white">
                                    <option value="all">All Formats</option>
                                    <option value="reel">Instagram Reel</option>
                                    <option value="linkedin">LinkedIn Post</option>
                                    <option value="thread">Twitter Thread</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-3 text-gray-300">Target Audience</label>
                                <input
                                    type="text"
                                    x-model="form.audience"
                                    placeholder="e.g., Marketing managers"
                                    class="w-full px-4 py-4 bg-gray-900/50 border border-gray-700 rounded-xl focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-500 transition-all"
                                >
                            </div>
                        </div>
                        <button type="submit" class="w-full py-4 btn-primary rounded-xl font-semibold text-lg text-white">
                            Generate Content Package
                        </button>
                    </form>
                </div>
            </div>

            <!-- Minimal Processing Animation -->
            <div x-show="isProcessing" class="max-w-3xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-6">Generating Content Package</h2>
                    <div class="brand-card rounded-2xl px-8 py-6 inline-block">
                        <div class="flex items-center space-x-3">
                            <div class="status-dot"></div>
                            <span class="text-lg text-gray-300">Researching:</span>
                            <span class="text-white font-semibold" x-text="form.topic"></span>
                        </div>
                    </div>
                </div>

                <!-- Clean Progress Bar -->
                <div class="mb-12">
                    <div class="flex justify-between text-sm mb-4 text-gray-400">
                        <span>Processing</span>
                        <span x-text="Math.round(progress) + '%'"></span>
                    </div>
                    <div class="w-full bg-gray-800 rounded-full h-2">
                        <div class="progress-line h-2 rounded-full" :style="'width: ' + progress + '%'"></div>
                    </div>
                </div>

                <!-- Minimal Workflow Steps -->
                <div class="grid md:grid-cols-3 gap-8 mb-12">
                    <template x-for="(step, index) in workflowSteps" :key="index">
                        <div class="text-center" :class="{'opacity-40': currentStep < index}">
                            <div class="w-16 h-16 mx-auto mb-4 rounded-2xl flex items-center justify-center transition-all duration-500"
                                 :class="currentStep === index ? 'brand-gradient' : currentStep > index ? 'bg-green-600' : 'bg-gray-700'">
                                <span class="text-2xl" x-text="currentStep > index ? '✓' : step.icon"></span>
                            </div>
                            <h3 class="font-medium mb-2" x-text="step.title"></h3>
                            <p class="text-sm text-gray-400" x-text="step.description"></p>
                            <div x-show="currentStep === index" class="mt-3">
                                <div class="w-1 h-1 bg-purple-500 rounded-full mx-auto pulse-minimal"></div>
                            </div>
                        </div>
                    </template>
                </div>

                <!-- Minimal Status -->
                <div class="brand-card rounded-xl p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="status-dot"></div>
                            <span class="text-sm font-medium">Live Status</span>
                        </div>
                        <div class="text-sm text-gray-400" x-text="statusUpdates.length > 0 ? statusUpdates[statusUpdates.length - 1].message : 'Initializing...'"></div>
                    </div>
                </div>
            </div>

            <!-- Professional Results Display -->
            <div x-show="showResults" class="max-w-5xl mx-auto">
                <div class="text-center mb-12">
                    <div class="w-16 h-16 brand-gradient rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <span class="text-2xl">✓</span>
                    </div>
                    <h2 class="text-3xl font-bold mb-4">Content Package Ready</h2>
                    <p class="text-lg text-gray-400">Your AI-generated content for: <span class="text-purple-400 font-semibold" x-text="form.topic"></span></p>
                </div>

                <!-- Clean Results Grid -->
                <div class="grid lg:grid-cols-2 gap-6 mb-12">
                    <!-- Instagram Content -->
                    <div class="brand-card rounded-2xl p-6 slide-up">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-rose-500 rounded-xl flex items-center justify-center">
                                    <span class="text-white text-sm font-bold">IG</span>
                                </div>
                                <h3 class="text-lg font-semibold">Instagram Reel</h3>
                            </div>
                            <button class="text-gray-400 hover:text-white transition-colors" @click="copyToClipboard(results.instagram)">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="bg-gray-900/50 rounded-xl p-4 text-sm text-gray-300 whitespace-pre-line max-h-64 overflow-y-auto" x-text="results.instagram"></div>
                    </div>

                    <!-- LinkedIn Content -->
                    <div class="brand-card rounded-2xl p-6 slide-up" style="animation-delay: 0.1s;">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
                                    <span class="text-white text-sm font-bold">LI</span>
                                </div>
                                <h3 class="text-lg font-semibold">LinkedIn Post</h3>
                            </div>
                            <button class="text-gray-400 hover:text-white transition-colors" @click="copyToClipboard(results.linkedin)">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="bg-gray-900/50 rounded-xl p-4 text-sm text-gray-300 whitespace-pre-line max-h-64 overflow-y-auto" x-text="results.linkedin"></div>
                    </div>

                    <!-- Twitter Content -->
                    <div class="brand-card rounded-2xl p-6 slide-up" style="animation-delay: 0.2s;">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-sky-500 rounded-xl flex items-center justify-center">
                                    <span class="text-white text-sm font-bold">X</span>
                                </div>
                                <h3 class="text-lg font-semibold">Twitter Thread</h3>
                            </div>
                            <button class="text-gray-400 hover:text-white transition-colors" @click="copyToClipboard(results.twitter)">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="bg-gray-900/50 rounded-xl p-4 text-sm text-gray-300 whitespace-pre-line max-h-64 overflow-y-auto" x-text="results.twitter"></div>
                    </div>

                    <!-- Research Summary -->
                    <div class="brand-card rounded-2xl p-6 slide-up" style="animation-delay: 0.3s;">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 brand-gradient rounded-xl flex items-center justify-center">
                                    <span class="text-white text-sm font-bold">AI</span>
                                </div>
                                <h3 class="text-lg font-semibold">Research Summary</h3>
                            </div>
                            <button class="text-gray-400 hover:text-white transition-colors" @click="copyToClipboard(results.research)">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="bg-gray-900/50 rounded-xl p-4 text-sm text-gray-300 whitespace-pre-line max-h-64 overflow-y-auto" x-text="results.research"></div>
                    </div>
                </div>

                <!-- Professional Action Buttons -->
                <div class="flex justify-center space-x-4">
                    <button @click="downloadResults()" class="px-8 py-3 bg-gray-700 hover:bg-gray-600 rounded-xl font-medium transition-all flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Download Package</span>
                    </button>
                    <button @click="resetWorkflow()" class="px-8 py-3 btn-primary rounded-xl font-medium transition-all">
                        Generate New Package
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function workflowApp() {
            return {
                form: {
                    topic: '',
                    contentType: 'all',
                    audience: ''
                },
                isProcessing: false,
                showResults: false,
                progress: 0,
                currentStep: 0,
                statusUpdates: [],
                results: {
                    instagram: '',
                    linkedin: '',
                    twitter: '',
                    research: ''
                },
                workflowSteps: [
                    {
                        icon: '🔍',
                        title: 'Research',
                        description: 'Multi-platform analysis'
                    },
                    {
                        icon: '🤖',
                        title: 'Generate',
                        description: 'AI content creation'
                    },
                    {
                        icon: '✓',
                        title: 'Complete',
                        description: 'Package ready'
                    }
                ],

                async startWorkflow() {
                    this.isProcessing = true;
                    this.progress = 0;
                    this.currentStep = 0;
                    this.statusUpdates = [];
                    
                    this.addStatus('🚀 Starting content research workflow...');
                    
                    // Simulate workflow progress
                    await this.simulateWorkflow();
                    
                    // Call actual n8n webhook
                    await this.callWorkflow();
                },

                async simulateWorkflow() {
                    const steps = [
                        { step: 0, progress: 25, message: 'Analyzing YouTube content...', delay: 1200 },
                        { step: 0, progress: 50, message: 'Processing Twitter data...', delay: 1000 },
                        { step: 1, progress: 70, message: 'Generating content...', delay: 1500 },
                        { step: 1, progress: 90, message: 'Optimizing for platforms...', delay: 1000 },
                        { step: 2, progress: 100, message: 'Package complete', delay: 800 }
                    ];

                    for (const stepData of steps) {
                        await new Promise(resolve => setTimeout(resolve, stepData.delay));
                        this.currentStep = stepData.step;
                        this.progress = stepData.progress;
                        this.addStatus(stepData.message);
                    }
                },

                async callWorkflow() {
                    try {
                        const response = await fetch('YOUR_N8N_WEBHOOK_URL', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                chatInput: this.form.topic,
                                contentType: this.form.contentType,
                                targetAudience: this.form.audience
                            })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            this.results = {
                                instagram: data.instagram_content || 'Instagram content generated successfully!',
                                linkedin: data.linkedin_content || 'LinkedIn post created!',
                                twitter: data.twitter_content || 'Twitter thread ready!',
                                research: data.research_summary || 'Research summary completed!'
                            };
                        }
                    } catch (error) {
                        console.error('Workflow error:', error);
                        this.addStatus('❌ Error occurred, using demo results...');
                        this.results = {
                            instagram: 'Demo Instagram reel script for: ' + this.form.topic,
                            linkedin: 'Demo LinkedIn post about: ' + this.form.topic,
                            twitter: 'Demo Twitter thread on: ' + this.form.topic,
                            research: 'Demo research summary for: ' + this.form.topic
                        };
                    }

                    this.isProcessing = false;
                    this.showResults = true;
                },

                addStatus(message) {
                    const now = new Date();
                    this.statusUpdates.push({
                        time: now.toLocaleTimeString(),
                        message: message
                    });
                },

                copyToClipboard(text) {
                    navigator.clipboard.writeText(text);
                    alert('Content copied to clipboard!');
                },

                downloadResults() {
                    const content = `Content Package for: ${this.form.topic}\n\n` +
                                  `INSTAGRAM REEL:\n${this.results.instagram}\n\n` +
                                  `LINKEDIN POST:\n${this.results.linkedin}\n\n` +
                                  `TWITTER THREAD:\n${this.results.twitter}\n\n` +
                                  `RESEARCH SUMMARY:\n${this.results.research}`;
                    
                    const blob = new Blob([content], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `content-package-${this.form.topic.replace(/\s+/g, '-')}.txt`;
                    a.click();
                },

                resetWorkflow() {
                    this.isProcessing = false;
                    this.showResults = false;
                    this.progress = 0;
                    this.currentStep = 0;
                    this.statusUpdates = [];
                    this.form = { topic: '', contentType: 'all', audience: '' };
                }
            }
        }
    </script>
</body>
</html>
