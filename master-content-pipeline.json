{"name": "Master Content Creation Pipeline", "nodes": [{"parameters": {"path": "content-research", "httpMethod": "POST", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1.1, "position": [-400, 200], "id": "main-webhook", "name": "Content Request Webhook", "webhookId": "master-content-pipeline"}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/research-agent", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "chatInput", "value": "={{ $json.topic || $json.chatInput }}"}]}, "options": {"timeout": 60000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-200, 200], "id": "research-call", "name": "Call Research Agent"}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/idea-generator", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "chatInput", "value": "Research Data: {{ $json.body }}\n\nGenerate creative content ideas based on this research."}]}, "options": {"timeout": 45000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, 200], "id": "idea-generator-call", "name": "Call Idea Generator"}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/instagram-specialist", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "chatInput", "value": "Content Ideas: {{ $json.body }}\n\nCreate Instagram-specific content based on these ideas."}]}, "options": {"timeout": 30000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [200, 100], "id": "instagram-call", "name": "Call Instagram Specialist"}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/linkedin-specialist", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "chatInput", "value": "Content Ideas: {{ $json.body }}\n\nCreate LinkedIn-specific professional content based on these ideas."}]}, "options": {"timeout": 30000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [200, 200], "id": "linkedin-call", "name": "Call LinkedIn Specialist"}, {"parameters": {"method": "POST", "url": "http://localhost:5678/webhook/twitter-specialist", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "chatInput", "value": "Content Ideas: {{ $json.body }}\n\nCreate Twitter-specific content based on these ideas."}]}, "options": {"timeout": 30000}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [200, 300], "id": "twitter-call", "name": "Call Twitter Specialist"}, {"parameters": {"jsCode": "// Combine all specialist outputs into comprehensive package\nconst researchData = $input.first().json;\nconst instagramData = $input.all()[1]?.json || {};\nconst linkedinData = $input.all()[2]?.json || {};\nconst twitterData = $input.all()[3]?.json || {};\n\n// Extract content from each specialist\nconst contentPackage = {\n  timestamp: new Date().toISOString(),\n  topic: researchData.topic || 'Content Research',\n  \n  research_summary: {\n    methodology: researchData.methodology || 'Multi-platform research conducted',\n    key_findings: researchData.key_findings || [],\n    content_opportunities: researchData.content_opportunities || [],\n    actionable_data: researchData.actionable_data || []\n  },\n  \n  instagram_content: {\n    reel_scripts: instagramData.reel_scripts || [],\n    carousel_posts: instagramData.carousel_posts || [],\n    story_sequences: instagramData.story_sequences || [],\n    optimization_strategy: instagramData.optimization_strategy || {}\n  },\n  \n  linkedin_content: {\n    thought_leadership_posts: linkedinData.thought_leadership_posts || [],\n    educational_carousels: linkedinData.educational_carousels || [],\n    professional_stories: linkedinData.professional_stories || [],\n    industry_analysis: linkedinData.industry_analysis || []\n  },\n  \n  twitter_content: {\n    viral_threads: twitterData.viral_threads || [],\n    standalone_tweets: twitterData.standalone_tweets || [],\n    quote_tweet_content: twitterData.quote_tweet_content || [],\n    engagement_strategy: twitterData.engagement_strategy || {}\n  },\n  \n  execution_summary: {\n    total_content_pieces: (\n      (instagramData.reel_scripts?.length || 0) +\n      (linkedinData.thought_leadership_posts?.length || 0) +\n      (twitterData.viral_threads?.length || 0)\n    ),\n    estimated_creation_time: '2-3 hours',\n    recommended_posting_schedule: {\n      instagram: 'Daily reels, 3x/week carousels',\n      linkedin: '2-3x/week posts',\n      twitter: 'Daily threads, 5-7 standalone tweets/day'\n    },\n    performance_predictions: {\n      instagram: 'High engagement potential with reels',\n      linkedin: 'Strong thought leadership positioning',\n      twitter: 'Viral thread potential identified'\n    }\n  }\n};\n\nreturn { json: contentPackage };"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [400, 200], "id": "content-compiler", "name": "Compile Content Package"}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json, null, 2) }}"}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [600, 200], "id": "final-response", "name": "Return Complete Package"}], "pinData": {}, "connections": {"Content Request Webhook": {"main": [[{"node": "Call Research Agent", "type": "main", "index": 0}]]}, "Call Research Agent": {"main": [[{"node": "Call Idea Generator", "type": "main", "index": 0}]]}, "Call Idea Generator": {"main": [[{"node": "Call Instagram Specialist", "type": "main", "index": 0}, {"node": "Call LinkedIn Specialist", "type": "main", "index": 0}, {"node": "Call Twitter Specialist", "type": "main", "index": 0}]]}, "Call Instagram Specialist": {"main": [[{"node": "Compile Content Package", "type": "main", "index": 0}]]}, "Call LinkedIn Specialist": {"main": [[{"node": "Compile Content Package", "type": "main", "index": 0}]]}, "Call Twitter Specialist": {"main": [[{"node": "Compile Content Package", "type": "main", "index": 0}]]}, "Compile Content Package": {"main": [[{"node": "Return Complete Package", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "master-pipeline-v1", "meta": {"templateCredsSetupCompleted": true}, "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tags": ["pipeline", "content", "master"]}