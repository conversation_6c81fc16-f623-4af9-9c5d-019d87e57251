{"name": "My workflow 2", "nodes": [{"parameters": {"trigger": ["message"], "channelId": {"__rl": true, "value": "C08GJ3CG342", "mode": "list", "cachedResultName": "slacktrigger_n8n_blog"}, "options": {}}, "type": "n8n-nodes-base.slackTrigger", "typeVersion": 1, "position": [-272, -2464], "id": "8b83a1b4-639c-4cb3-b75b-ab3b10be5cb3", "name": "<PERSON><PERSON><PERSON>", "webhookId": "c432a5ad-cd77-4751-ba17-60d5e8dae2d2"}, {"parameters": {"public": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [2288, -704], "id": "72c62304-428a-4506-8050-da77c78da190", "name": "When chat message received", "webhookId": "202a90db-971d-40e5-a86d-6c3e748f3a40"}, {"parameters": {"jsCode": "// Extract and merge JSON from Code Node output\nconst jsonData = $input.all().map(item => item.json);\n\n// Function to format structured text for Google Docs\nfunction formatContent(data) {\n    let content = `# Research\\n\\n`;\n\n    data.forEach(section => {\n        content += `## ${section.section}\\n`;\n\n        // Loop through all keys except 'section'\n        Object.keys(section).forEach(key => {\n            if (key !== \"section\") {\n                if (Array.isArray(section[key])) {\n                    content += `- **${key.replace(/_/g, \" \")}**:\\n  - ` + section[key].join(\"\\n  - \") + `\\n\\n`;\n                } else {\n                    content += `- **${key.replace(/_/g, \" \")}**: ${section[key]}\\n\\n`;\n                }\n            }\n        });\n    });\n\n    return content.trim();\n}\n\n// Generate the formatted text output\nconst documentText = formatContent(jsonData);\n\n// Return structured text for Google Docs\nreturn [{\n    json: {\n        document_name: \"Research\",\n        content: documentText // This ensures 'content' is always returned\n    }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [5360, -176], "id": "d2bb81e6-8487-463c-bb52-e6087d10b4e8", "name": "Code2"}, {"parameters": {"content": "Analyze YouTube & X for Topics and Context", "height": 1180, "width": 1840, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2256, -800], "id": "a2ffbca1-5347-49aa-8ffa-8b327b9813ed", "name": "Sticky Note1"}, {"parameters": {"content": "Generate Channel Specific Content\n", "height": 1180, "width": 720, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [5712, -800], "id": "2e53fa91-98b2-41ec-a69f-894ce8e85011", "name": "Sticky Note3"}, {"parameters": {"content": "Human in the Loop Review & Automated Distribution\n\n\n\n\n\n\n", "height": 1436, "width": 2380, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [8848, -384], "id": "766cb97d-63c4-4a3f-a69f-0c03cbeedc9e", "name": "Sticky Note4"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/streamers~youtube-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"dateFilter\": \"month\",\n    \"downloadSubtitles\": false,\n    \"hasCC\": false,\n    \"hasLocation\": false,\n    \"hasSubtitles\": false,\n    \"is360\": false,\n    \"is3D\": false,\n    \"is4K\": false,\n    \"isBought\": false,\n    \"isHD\": false,\n    \"isHDR\": false,\n    \"isLive\": false,\n    \"isVR180\": false,\n    \"lengthFilter\": \"between420\",\n    \"maxResultStreams\": 0,\n    \"maxResults\": 10,\n    \"maxResultsShorts\": 10,\n    \"preferAutoGeneratedSubtitles\": true,\n    \"saveSubsToKVS\": true,\n    \"searchQueries\": [\n        \"n8n\"\n    ],\n    \"sortingOrder\": \"views\",\n    \"subtitlesLanguage\": \"en\",\n    \"videoType\": \"video\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2688, -704], "id": "7fadd211-6a4f-45af-ad03-32cbe0929ac8", "name": "HTTP Request"}, {"parameters": {"url": "https://api.apify.com/v2/acts/pintostudio~youtube-transcript-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"videoUrl\": \"{{ $json.url }}\"\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [3312, -512], "id": "2518c491-a14e-4158-9c03-8ce7d23ce980", "name": "HTTP Request2"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [2912, -704], "id": "2ac4b23f-40bc-4167-86fa-b41cacdb617e", "name": "Loop Over Items"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/danek~twitter-scraper-ppr/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"includeReplies\": false,\n    \"includeRetweets\": false,\n    \"max_posts\": 10,\n    \"username\": \"username\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2656, -240], "id": "dcd92680-6824-4920-9e8a-c51459b744ef", "name": "HTTP Request3"}, {"parameters": {"method": "POST", "url": "https://api.apify.com/v2/acts/trudax~reddit-scraper/run-sync-get-dataset-items?token=**********************************************", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n    \"searches\": [\n        \"{{ $json.chatInput }}\",\n        \"{{ $json.chatInput }} marketing automation\",\n        \"{{ $json.chatInput }} AI tools\",\n        \"{{ $json.chatInput }} workflow\"\n    ],\n    \"searchPosts\": true,\n    \"searchComments\": false,\n    \"searchCommunities\": false,\n    \"searchUsers\": false,\n    \"sort\": \"hot\",\n    \"time\": \"week\",\n    \"includeNSFW\": false,\n    \"maxItems\": 25,\n    \"maxPostCount\": 15,\n    \"maxComments\": 0,\n    \"skipComments\": true,\n    \"skipUserPosts\": true,\n    \"skipCommunity\": true,\n    \"scrollTimeout\": 30,\n    \"debugMode\": false,\n    \"proxy\": {\n        \"useApifyProxy\": true,\n        \"apifyProxyGroups\": [\"RESIDENTIAL\"]\n    }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2656, 80], "id": "reddit-scraper-node", "name": "Reddit Research"}, {"parameters": {"jsCode": "// Process Reddit data to extract valuable marketing insights\nconst items = $input.all();\n\n// Filter and process Reddit posts for marketing relevance\nconst processedReddit = items.map(item => {\n  const post = item.json;\n  \n  // Calculate engagement score\n  const engagementScore = (post.score || 0) + (post.numberOfComments || 0);\n  \n  // Extract key information\n  return {\n    json: {\n      title: post.title || '',\n      text: post.text || post.selftext || '',\n      subreddit: post.subreddit || '',\n      score: post.score || 0,\n      comments: post.numberOfComments || 0,\n      engagement_score: engagementScore,\n      url: post.url || '',\n      created_utc: post.createdAt || post.created_utc,\n      // Filter for marketing-relevant content\n      is_marketing_relevant: (\n        (post.title || '').toLowerCase().includes('marketing') ||\n        (post.title || '').toLowerCase().includes('automation') ||\n        (post.title || '').toLowerCase().includes('ai') ||\n        (post.title || '').toLowerCase().includes('n8n') ||\n        (post.subreddit || '').toLowerCase().includes('marketing') ||\n        (post.subreddit || '').toLowerCase().includes('entrepreneur')\n      )\n    }\n  };\n}).filter(item => \n  // Only keep marketing-relevant posts with decent engagement\n  item.json.is_marketing_relevant && \n  item.json.engagement_score > 5 &&\n  item.json.title.length > 10\n).sort((a, b) => \n  // Sort by engagement score descending\n  b.json.engagement_score - a.json.engagement_score\n).slice(0, 10); // Keep top 10 posts\n\nreturn processedReddit;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3200, 80], "id": "reddit-processor", "name": "Process Reddit Data"}, {"parameters": {"jsCode": "// Access all input items (each one is a video)\nconst items = $input.all();\n\n// For each video item, join the captions from data[]\nconst result = items.map(item => {\n  const data = item.json.data;\n\n  // Safely join text only if data is an array\n  const fullCaption = Array.isArray(data)\n    ? data.map(d => d.text).join(' ')\n    : '';\n\n  return {\n    json: {\n      full_caption: fullCaption\n    }\n  };\n});\n\nreturn result;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3216, -720], "id": "********-b64b-4812-aad5-34582fcfaf5e", "name": "Code"}, {"parameters": {"model": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "chatgpt-4o-latest"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [4432, 48], "id": "702c7ce4-bd21-4ada-abae-d3aee7d27aaf", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {"jsCode": "// Get all input items\nconst items = $input.all();\n\n// Get current time and calculate threshold for 10 days ago\nconst now = new Date();\nconst tenDaysAgo = new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000); // 10 days in ms\n\n// Filter based on both `reply_to` and `created_at`\nconst filtered = items.filter(item => {\n  const replyTo = item.json.reply_to;\n  const createdAt = item.json.created_at;\n\n  const isOriginalPost =\n    replyTo === undefined ||\n    replyTo === null ||\n    replyTo === '[undefined]' ||\n    (Array.isArray(replyTo) && replyTo.length === 0);\n\n  const isRecent =\n    createdAt && new Date(createdAt) >= tenDaysAgo;\n\n  return isOriginalPost && isRecent;\n});\n\nreturn filtered;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [3200, -240], "id": "1dc1186b-fe80-4b16-bb92-80a3050aedf3", "name": "Code1"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {"includeUnpaired": true}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [3792, -224], "id": "25ace517-9180-46aa-bf47-04bd4ac27276", "name": "Merge5"}, {"parameters": {"fieldToSplitOut": "chatInput", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [2480, -704], "id": "d385a950-e8ee-45d5-ba6a-db1060ca6eec", "name": "Split Out"}, {"parameters": {"promptType": "define", "text": "=Youtube transcript: {{ $json.full_caption }}\n\nTwitter - {{ $json.text }}\n\nReddit Discussion: {{ $json.title }} - {{ $json.text }} (Score: {{ $json.score }}, Comments: {{ $json.comments }})\n\n### TASK:\nUsing the above multi-platform research, generate a list of **actionable content ideas that are strictly related to marketing nothing technical**. For each idea, include:\n\n- **Title**: Short and clear.\n- **Hook**: Scroll-stopping idea to pull people in.\n- **Format**: Suggest whether it's better as a video, one-pager, thread, etc.\n- **Angle**: What unique point of view (around how marketers can scale marketing efforts with AI using n8n for specific marketing use case)\n\nNote: Only pick transcripts that are in english \n\n- Avoid technical jargons and technical topics\n- Suggest marketing use cases for beginners who are marketers\n- Marketing Angle, hook, title etc for non-technical marketers\n\n\n**A. YouTube Transcripts from Other Creators**  \nThese are insights from videos that are trending or educational. Use these to understand:\n- What problems they’re solving\n- What frameworks or tools are used\n- What kinds of hooks or structures they follow\n\n**B. My Top Performing Twitter Posts**  \nThese are short-form content examples that resonated well with our audience. Use these to:\n- Extract what tones, angles, or use cases work\n- Mirror themes, language style, and audience engagement patterns\n\n**C. Reddit Marketing Discussions**  \nThese are real conversations from marketing communities showing:\n- Pain points marketers are discussing\n- Questions they're asking\n- Solutions they're seeking\n- Trending topics in marketing automation\n\nUse Reddit insights to identify content gaps and address real marketer concerns.\n\n\n", "options": {"systemMessage": "You are an expert content strategist helping create high-impact content tailored to a marketing and automation-savvy audience.\n\n### OBJECTIVE:\nGenerate **actionable content ideas** based on:\n1. YouTube transcripts from other creators in our niche\n2. My best-performing Twitter posts\n3. Reddit marketing discussions and community insights\n\nThe output should help us create **videos and one-pagers** that:\n- Are useful and practical\n- Mirror themes and tones that work for our audience\n- Address real pain points from Reddit discussions\n- Explore new but relevant ideas\n- Lean into content formats that already perform well for us"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [4416, -192], "id": "d894856f-88e2-40f3-9e93-2a099d43b436", "name": "Content idea generator"}, {"parameters": {"promptType": "define", "text": "=using the  {{ $json.output }} starting point \n\nResearch and find marketing use cases - around how marketers can scale marketing efforts with AI using n8n for specific marketing use case like ad campaign, scaling outbound, sclaing content creation\n\nPlease conduct deep research on the topic above. Specifically:\n\n- Key Trends and Insights\n- Marketing use cases\n- What’s currently happening in the industry or niche?\n- Are there stats, frameworks, or case studies worth referencing?\n- Popular Opinions vs. Expert Takes\n- What are people saying on social platforms or forums?\n- Are there any contrarian, expert-backed, or field-tested perspectives?\n- Data, Stats, or Real Examples\n- Include any performance benchmarks, studies, or business use cases\n- Source Links or Summarized Citations\n- If quoting or citing, include the origin (author, source, link)\n\nOutput fomat\n\na. topic\nb. key insights\nc. expert takes\nd. Supporting data with source\ne. citations\nf. use cases", "options": {"systemMessage": "You are a senior research strategist trained in high-depth content discovery and synthesis.\n\nYour role is to explore authoritative sources, trends, case studies, and opinion patterns related to a specific topic or query.\n\nYou always prioritize factual accuracy, real-world examples, and strategic relevance over surface-level summaries.\n\nYour insights are designed to inform downstream content agents who will create long-form posts, thought leadership, or campaign assets based on your research.\n\nDo not write final posts or content. Your output should consist of organized, useful findings that serve as a research base."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [4960, -160], "id": "c08d73a4-b7ba-474b-ab2d-2201fb0d8720", "name": "Research agent"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {"includeUnpaired": true}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [5520, -560], "id": "ed802674-7119-4aa1-82b1-c498db0554ae", "name": "Merge1"}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "options": {"includeUnpaired": true}}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [7648, -512], "id": "c073d1f9-a76f-4581-ac38-c435ad6aeef0", "name": "Merge8"}, {"parameters": {"content": "Research for Additional Data Points & Generate Specific Post Ideas", "height": 1180, "width": 1620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [4096, -800], "id": "0882a9db-23d9-47ec-b98d-acf46a3eda38", "name": "Sticky Note2"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "content_idea"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [5056, 64], "id": "7c1e88ab-da37-4a9f-8c32-24cf647a785e", "name": "Simple Memory"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "perplexity_research"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [5936, -240], "id": "02447c5e-4d72-40ac-ba82-5f4166ecdb1e", "name": "Simple Memory1"}, {"parameters": {"model": {"__rl": true, "value": "chatgpt-4o-latest", "mode": "list", "cachedResultName": "chatgpt-4o-latest"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [5712, -256], "id": "344adbaf-0d27-4ef9-b3c5-0fb1b8472489", "name": "OpenAI Chat Model3", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {"promptType": "define", "text": "=Based on your role and the brand guide, generate a complete Instagram Reel package from the following content idea:\n\n**Content Idea:** \"How our marketing team uses n8n to automate the creation of 100 personalized ad creatives in under an hour. Focus on the scaling and cost-saving aspects for e-commerce brands.\"", "hasOutputParser": true, "options": {"systemMessage": "# ROLE & GOAL\nYou are a senior brand strategist and scriptwriter for <PERSON><PERSON>, the founder of the Gen-AI creative studio, Offbeat Origins. Your task is to transform a single content idea into a complete, ready-to-produce Instagram Reel package.\n\n# CORE DIRECTIVES\nYou MUST internalize and strictly adhere to the <PERSON><PERSON> G<PERSON>h brand guide. Your output must reflect his persona, brand objectives, audience, voice, and content pillars. Every element you create must feel like it came directly from him.\n\n# BRAND GUIDE SUMMARY (Your Core Knowledge)\n- **Persona:** <PERSON><PERSON>, a Gen-AI-driven brand storyteller, not a coder. He's an accessible mentor and a results-oriented creative leader.\n- **Studio (Offbeat Origins):** A Gen-AI creative studio for fashion/retail/DTC brands, delivering visual assets faster and cheaper than traditional methods.\n- **Audience:** CXOs/Founders (care about ROI), Brand/Creative Leads (crave process recipes), E-commerce/Growth Heads (love data/CTR), and Peer Marketers. Primary geography: India & GCC.\n- **Voice & Tone:** Business-casual English. Punchy, concrete, optimistic but not hype. Wry humor is okay, but no cynicism. Avoid jargon. Speak directly and confidently, like a successful practitioner sharing hard-earned wisdom.\n- **Content Pillars:** Founder’s Lens, AI for Creatives, BTS/Case Studies, Reality Checks, News Roundups.\n- **Visuals:** Palette is Electric Blue (#007AFF), Magenta (#FF4F88), Charcoal (#222), White (#FFFFFF). Fonts are DM Sans and Inter.\n\n# TASK: GENERATE A COMPLETE INSTAGRAM REEL PACKAGE\nFrom the user-provided content idea, you will generate a complete package. The Reel should be engaging, provide actionable insights, and align with a specific content pillar.\n\n# OUTPUT RULES (NON-NEGOTIABLE)\n1.  Your entire output MUST be a single, valid JSON object.\n2.  Do NOT include any text, conversation, or explanations outside of this JSON structure.\n3.  The Reel script must be concise, aiming for a total duration of 45 seconds or less.\n4.  The caption must be engaging, provide value, and include a clear Call-to-Action (CTA) that aligns with brand objectives (e.g., prompting a \"AI\" DM).\n\n# REQUIRED JSON OUTPUT SCHEMA:\n```json\n{\n  \"content_pillar\": \"Identify and state which of the 5 content pillars this Reel belongs to (e.g., 'AI for Creatives & Brands').\",\n  \"reel_title\": \"A short, punchy, curiosity-driven title for the Reel (max 70 characters).\",\n  \"hook\": \"The first 3 seconds of the video. A bold statement or a problem-focused question to stop the scroll.\",\n  \"script_with_b_roll\": [\n    {\n      \"timestamp\": \"0s - 7s\",\n      \"voiceover\": \"Script for the first segment. Direct, confident, and punchy.\",\n      \"b_roll_visual\": \"Describe the on-screen action or visual. e.g., 'Nikesh talking to camera, overlay of a Notion brief.' or 'Screen recording of an n8n workflow in action.'\"\n    },\n    {\n      \"timestamp\": \"7s - 18s\",\n      \"voiceover\": \"Script for the second segment. This should be the core insight or tactical advice.\",\n      \"b_roll_visual\": \"Describe the on-screen action or visual. e.g., 'Side-by-side comparison of a traditional shoot cost vs. AI-generated asset cost.' or 'Fast-cut montage of 10 different AI-generated images.'\"\n    },\n    {\n      \"timestamp\": \"18s - 25s\",\n      \"voiceover\": \"Script for the final segment, leading to the CTA.\",\n      \"b_roll_visual\": \"Describe the on-screen action or visual. e.g., 'Nikesh back on camera, smiling confidently. Text overlay with a key takeaway.'\"\n    }\n  ],\n  \"instagram_caption\": \"A detailed, multi-paragraph Instagram caption. It must be formatted with line breaks for readability, use relevant emojis sparingly, and end with a strong CTA like 'DM me ‘AI’ for a free audit'.\",\n  \"suggested_hashtags\": [\n    \"A list of 5-7 highly relevant hashtags. Mix broader topics with niche terms. e.g., '#GenAI', '#BrandStrategy', '#MarketingAutomation', '#CreativeTech', '#OffbeatOrigins'.\"\n  ]\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [5856, -464], "id": "048befde-235f-48db-be59-9adcfb1ebe93", "name": "instagram post generating agent"}, {"parameters": {"jsCode": "// n8n Code Node: Parse Multiple JSON Objects from a Single String\n\n// The AI's full output string is in the 'output' property\nconst rawOutputString = $input.first().json.output || '';\n\n// This regex finds every block of text that starts with '{' and ends with '}'\n// The 'g' flag ensures it finds all occurrences, not just the first.\n// The 's' flag allows the dot (.) to match newlines, which is crucial here.\nconst jsonRegex = /({[\\s\\S]*?})/g;\n\n// Find all matches in the raw string\nconst jsonStringMatches = rawOutputString.match(jsonRegex);\n\nconst outputItems = [];\n\n// Check if any JSON strings were found\nif (jsonStringMatches && Array.isArray(jsonStringMatches)) {\n  for (const jsonString of jsonStringMatches) {\n    try {\n      // For each matched string, try to parse it into a real JSON object\n      const parsedJson = JSON.parse(jsonString);\n      \n      // Create a new n8n item for each successfully parsed object\n      outputItems.push({\n        json: parsedJson\n      });\n    } catch (error) {\n      // If parsing fails for a specific block, log the error but don't stop the workflow\n      console.log('Could not parse a JSON block:', error);\n      console.log('Problematic JSON string:', jsonString);\n    }\n  }\n} else {\n  console.log('No JSON objects found in the AI output string.');\n}\n\n// Return the array of new n8n items.\n// Each item will be one of the parsed JSON content packages.\nreturn outputItems;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [6496, -464], "id": "107eaba2-3aee-4e24-9775-356e9161ca38", "name": "Insta formatted output1"}, {"parameters": {"folderId": "1XTbuajQ03Hf6JZiNsVFshUKvFUgKhfcd", "title": "Instagram posts"}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [7360, -592], "id": "297ee91e-197a-4298-8cdf-a0371cb18b05", "name": "doc creation1", "executeOnce": true, "credentials": {"googleDocsOAuth2Api": {"id": "TUuokkNIbm5st1kM", "name": "Google Docs account"}}}, {"parameters": {"operation": "update", "documentURL": "=https://docs.google.com/document/d/{{ $json.id }}/edit", "actionsUi": {"actionFields": [{"action": "insert", "text": "={{ $json.linkedin_post }}"}]}}, "type": "n8n-nodes-base.googleDocs", "typeVersion": 2, "position": [7952, -512], "id": "88aba824-eac3-425b-a008-da0f80edec65", "name": "content doc1", "credentials": {"googleDocsOAuth2Api": {"id": "TUuokkNIbm5st1kM", "name": "Google Docs account"}}}, {"parameters": {"model": "sonar-deep-research", "messages": {"message": [{}]}, "options": {}, "requestOptions": {}}, "type": "n8n-nodes-base.perplexityTool", "typeVersion": 1, "position": [5264, 112], "id": "88731ace-8a68-4bfb-a6e7-2eac413ff1cf", "name": "Message a model in Perplexity", "credentials": {"perplexityApi": {"id": "OYG1PL5F5M7FQKon", "name": "Perplexity account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4.1-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [4864, 96], "id": "751064fa-007f-477b-baea-77c60209378e", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "LUi6tDrmck95HolT", "name": "OpenAi account 2"}}}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Code2": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "HTTP Request": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Code", "type": "main", "index": 0}], [{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Merge5", "type": "main", "index": 1}]]}, "HTTP Request3": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Content idea generator", "type": "ai_languageModel", "index": 0}]]}, "Code1": {"main": [[{"node": "Merge5", "type": "main", "index": 0}]]}, "Merge5": {"main": [[{"node": "Content idea generator", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}, {"node": "HTTP Request3", "type": "main", "index": 0}, {"node": "Reddit Research", "type": "main", "index": 0}]]}, "Content idea generator": {"main": [[{"node": "Research agent", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 0}]]}, "Research agent": {"main": [[{"node": "Code2", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "instagram post generating agent", "type": "main", "index": 0}]]}, "Merge8": {"main": [[{"node": "content doc1", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Research agent", "type": "ai_memory", "index": 0}]]}, "Simple Memory1": {"ai_memory": [[{"node": "instagram post generating agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model3": {"ai_languageModel": [[{"node": "instagram post generating agent", "type": "ai_languageModel", "index": 0}]]}, "instagram post generating agent": {"main": [[{"node": "Insta formatted output1", "type": "main", "index": 0}]]}, "Insta formatted output1": {"main": [[{"node": "doc creation1", "type": "main", "index": 0}, {"node": "Merge8", "type": "main", "index": 1}]]}, "doc creation1": {"main": [[{"node": "Merge8", "type": "main", "index": 0}]]}, "content doc1": {"main": [[]]}, "Message a model in Perplexity": {"ai_tool": [[{"node": "Research agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Research agent", "type": "ai_languageModel", "index": 0}]]}, "Reddit Research": {"main": [[{"node": "Process Reddit Data", "type": "main", "index": 0}]]}, "Process Reddit Data": {"main": [[{"node": "Merge5", "type": "main", "index": 2}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "62a41821-3503-4550-8187-65869<PERSON><PERSON><PERSON><PERSON>", "meta": {"templateCredsSetupCompleted": true, "instanceId": "733f60c7b0c4609e60ec0cf958e7904eebc14a5810e0081aca3a300d7426df10"}, "id": "ff9L7BTSGNUb5LxT", "tags": []}