{"name": "Nikesh AI Marketing Content Creator", "nodes": [{"id": "manual-trigger", "name": "Start Content Creation", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 400], "parameters": {}}, {"id": "set-content-params", "name": "Set Content Parameters", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 400], "parameters": {"assignments": {"assignments": [{"id": "content_focus", "name": "content_focus", "value": "{{ $json.content_focus || 'latest AI marketing tools and trends' }}", "type": "string"}, {"id": "content_type", "name": "content_type", "value": "{{ $json.content_type || 'educational_reel' }}", "type": "string"}, {"id": "target_audience", "name": "target_audience", "value": "{{ $json.target_audience || 'CXOs and Brand Leaders in India' }}", "type": "string"}, {"id": "content_pillar", "name": "content_pillar", "value": "{{ $json.content_pillar || 'AI for Creatives & Brands' }}", "type": "string"}]}}}, {"id": "perplexity-ai-trends", "name": "Research AI Marketing Trends", "type": "n8n-nodes-base.perplexity", "typeVersion": 1, "position": [680, 300], "parameters": {"model": "sonar-pro", "messages": {"message": [{"role": "system", "content": "You are an AI marketing research expert. Focus on the latest trends, tools, and case studies in AI-powered marketing and branding, especially relevant to Indian and GCC markets."}, {"role": "user", "content": "Research the latest trends and developments in {{ $node['Set Content Parameters'].json.content_focus }} for {{ $node['Set Content Parameters'].json.target_audience }}. Focus on:\n\n1. Latest AI marketing tools launched in the past 30 days\n2. Successful case studies with measurable ROI\n3. Trending techniques in AI-powered branding\n4. Tools specifically useful for fashion, retail, and DTC brands\n5. Cost-effective AI solutions for small to medium businesses\n\nProvide specific examples, statistics, and actionable insights. Include any relevant pricing, features, and real-world applications."}]}}}, {"id": "apify-instagram-research", "name": "Scrape Viral AI Content", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 450], "parameters": {"url": "https://api.apify.com/v2/acts/apify~instagram-scraper/run-sync-get-dataset-items", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_APIFY_TOKEN"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonParameters": {"parameters": [{"name": "hashtags", "value": "[\"aimarketing\", \"aitools\", \"marketingai\", \"brandingai\", \"aiforbrands\"]"}, {"name": "resultsLimit", "value": 20}, {"name": "searchType", "value": "hashtag"}]}}}, {"id": "apify-linkedin-research", "name": "Scrape LinkedIn AI Posts", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 600], "parameters": {"url": "https://api.apify.com/v2/acts/apify~linkedin-posts-scraper/run-sync-get-dataset-items", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_APIFY_TOKEN"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonParameters": {"parameters": [{"name": "searchTerms", "value": "[\"AI marketing tools\", \"artificial intelligence branding\", \"AI for business\"]"}, {"name": "count", "value": 15}]}}}, {"id": "merge-research", "name": "Merge All Research", "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [900, 450], "parameters": {"mode": "combine", "combineBy": "combineAll"}}, {"id": "analyze-viral-content", "name": "Ana<PERSON><PERSON> Viral Pattern<PERSON>", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1120, 450], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are a content strategist specializing in AI marketing content. Analyze viral content patterns and extract insights for creating engaging posts that generate leads for AI marketing services."}, {"role": "user", "content": "Based on this research data:\n\nPerplexity Research: {{ $node['Research AI Marketing Trends'].json.choices[0].message.content }}\n\nInstagram Data: {{ $node['Scrape Viral AI Content'].json }}\n\nLinkedIn Data: {{ $node['Scrape LinkedIn AI Posts'].json }}\n\nAnalyze and provide:\n1. Top 5 viral content patterns in AI marketing\n2. Most engaging hook formats\n3. Common visual elements that drive engagement\n4. Hashtag strategies that work\n5. Content angles that generate business inquiries\n6. Platform-specific optimization tips\n\nFocus on content that would appeal to {{ $node['Set Content Parameters'].json.target_audience }} and align with <PERSON><PERSON>'s brand voice: business-casual, concrete, optimistic without hype."}]}, "maxTokens": 1000, "temperature": 0.7}}, {"id": "generate-instagram-content", "name": "Generate Instagram Content", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1340, 300], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are <PERSON><PERSON>'s content creator. Generate Instagram content that positions him as a thought leader in AI marketing. Use his brand voice: business-casual, concrete, optimistic without hype. Focus on actionable insights that generate leads from CXOs and brand leaders."}, {"role": "user", "content": "Based on this analysis: {{ $node['Analyze Viral Patterns'].json.choices[0].message.content }}\n\nCreate Instagram content for {{ $node['Set Content Parameters'].json.content_pillar }}:\n\n**REEL SCRIPT (30-45 seconds):**\n- Hook (first 3 seconds)\n- 3 main points with transitions\n- Strong CTA for DMs\n- Visual cues for editing\n\n**CAPTION:**\n- Engaging opening\n- Key insights with line breaks\n- Call-to-action: 'DM \"AI\" for free audit'\n- 5-8 relevant hashtags\n\n**CAROUSEL ALTERNATIVE (6 slides):**\n- Slide titles and key points\n- Visual suggestions\n\nFocus on: {{ $node['Set Content Parameters'].json.content_focus }}\nTarget: {{ $node['Set Content Parameters'].json.target_audience }}"}]}, "maxTokens": 1200, "temperature": 0.8}}, {"id": "generate-linkedin-content", "name": "Generate LinkedIn Content", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1340, 450], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "Create LinkedIn content for Nikesh Ghosh that appeals to CXOs, founders, and marketing leaders. Focus on business value, ROI, and practical AI implementation. Use professional tone with concrete examples."}, {"role": "user", "content": "Using this research: {{ $node['Analyze Viral Patterns'].json.choices[0].message.content }}\n\nCreate LinkedIn post about {{ $node['Set Content Parameters'].json.content_focus }}:\n\n**POST STRUCTURE:**\n- Attention-grabbing opening line\n- Problem/challenge statement\n- 3-4 actionable insights with specific examples\n- ROI/business impact data\n- Professional CTA for connection/audit\n\n**TONE:** Professional but approachable, data-driven, solution-focused\n**LENGTH:** 150-200 words\n**HASHTAGS:** 3-5 professional hashtags\n\nInclude specific metrics, tool names, and real-world applications that would interest {{ $node['Set Content Parameters'].json.target_audience }}."}]}, "maxTokens": 800, "temperature": 0.7}}, {"id": "generate-twitter-content", "name": "Generate Twitter Content", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1340, 600], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "Create Twitter/X content for <PERSON><PERSON> Ghosh. Focus on quick insights, hot takes, and thread-worthy content about AI marketing. Keep it punchy and engaging."}, {"role": "user", "content": "Based on: {{ $node['Analyze Viral Patterns'].json.choices[0].message.content }}\n\nCreate Twitter content about {{ $node['Set Content Parameters'].json.content_focus }}:\n\n**SINGLE TWEET:**\n- One powerful insight (under 280 characters)\n- Include relevant hashtags\n\n**THREAD (5-7 tweets):**\n- Hook tweet\n- 3-5 insight tweets with examples\n- Conclusion with CTA\n\n**QUOTE TWEET READY:**\n- Commentary on AI marketing trend\n- Personal take or prediction\n\nFocus on insights that would get retweeted by {{ $node['Set Content Parameters'].json.target_audience }}. Include specific tools, metrics, or predictions."}]}, "maxTokens": 600, "temperature": 0.8}}, {"id": "weekly-ai-news", "name": "Generate Weekly AI News", "type": "n8n-nodes-base.perplexity", "typeVersion": 1, "position": [1340, 750], "parameters": {"model": "sonar-deep-research", "messages": {"message": [{"role": "system", "content": "You are an AI marketing news curator. Focus on the most important developments in AI marketing, branding, and creative tools from the past week that would impact business decisions."}, {"role": "user", "content": "Create a weekly AI marketing news roundup for the past 7 days. Focus on:\n\n1. New AI marketing tools launched\n2. Major updates to existing platforms (ChatGPT, Midjourney, etc.)\n3. Significant funding/acquisitions in AI marketing space\n4. Regulatory changes affecting AI in marketing\n5. Notable case studies or campaign results\n\nFormat as:\n- 5-7 key stories\n- Brief summary for each (2-3 sentences)\n- Why it matters for marketers/brands\n- Action items or implications\n\nTarget audience: Marketing leaders and CXOs who need to stay informed but don't have time to follow every development."}]}}}, {"id": "compile-content-package", "name": "Compile Content Package", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1560, 450], "parameters": {"assignments": {"assignments": [{"id": "username", "name": "username", "value": "@nikesh<PERSON>osh", "type": "string"}, {"id": "content_focus", "name": "content_focus", "value": "{{ $node['Set Content Parameters'].json.content_focus }}", "type": "string"}, {"id": "content_pillar", "name": "content_pillar", "value": "{{ $node['Set Content Parameters'].json.content_pillar }}", "type": "string"}, {"id": "instagram_content", "name": "instagram_content", "value": "{{ $node['Generate Instagram Content'].json.choices[0].message.content }}", "type": "string"}, {"id": "linkedin_content", "name": "linkedin_content", "value": "{{ $node['Generate LinkedIn Content'].json.choices[0].message.content }}", "type": "string"}, {"id": "twitter_content", "name": "twitter_content", "value": "{{ $node['Generate Twitter Content'].json.choices[0].message.content }}", "type": "string"}, {"id": "weekly_ai_news", "name": "weekly_ai_news", "value": "{{ $node['Generate Weekly AI News'].json.choices[0].message.content }}", "type": "string"}, {"id": "viral_insights", "name": "viral_insights", "value": "{{ $node['Analyze Viral Patterns'].json.choices[0].message.content }}", "type": "string"}, {"id": "created_date", "name": "created_date", "value": "{{ $now }}", "type": "string"}]}}}, {"id": "save-to-content-sheet", "name": "Save to Content Calendar", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1780, 400], "parameters": {"operation": "append", "documentId": "YOUR_GOOGLE_SHEET_ID", "sheetName": "Content Calendar", "range": "A:J", "options": {"valueInputOption": "USER_ENTERED"}, "columns": {"mappingMode": "defineBelow", "value": {"Username": "={{ $json.username }}", "Content Focus": "={{ $json.content_focus }}", "Content Pillar": "={{ $json.content_pillar }}", "Instagram Content": "={{ $json.instagram_content }}", "LinkedIn Content": "={{ $json.linkedin_content }}", "Twitter Content": "={{ $json.twitter_content }}", "Weekly AI News": "={{ $json.weekly_ai_news }}", "Viral Insights": "={{ $json.viral_insights }}", "Created Date": "={{ $json.created_date }}", "Status": "Ready for Review"}}}}, {"id": "send-content-summary", "name": "Send Content Summary", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1780, 550], "parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "Weekly AI Content Package Ready - {{ $json.content_focus }}", "message": "Hi <PERSON><PERSON>,\n\nYour weekly AI marketing content package is ready! 🚀\n\n📊 CONTENT FOCUS: {{ $json.content_focus }}\n🎯 PILLAR: {{ $json.content_pillar }}\n📅 CREATED: {{ $json.created_date }}\n\n📱 INSTAGRAM CONTENT:\n{{ $json.instagram_content }}\n\n💼 LINKEDIN CONTENT:\n{{ $json.linkedin_content }}\n\n🐦 TWITTER CONTENT:\n{{ $json.twitter_content }}\n\n📰 WEEKLY AI NEWS:\n{{ $json.weekly_ai_news }}\n\n🔥 VIRAL INSIGHTS:\n{{ $json.viral_insights }}\n\n✅ All content has been saved to your Google Sheets content calendar.\n\nReady to establish thought leadership and generate those inbound leads!\n\nBest,\nYour AI Content Assistant", "options": {}}}], "connections": {"Start Content Creation": {"main": [[{"node": "Set Content Parameters", "type": "main", "index": 0}]]}, "Set Content Parameters": {"main": [[{"node": "Research AI Marketing Trends", "type": "main", "index": 0}, {"node": "Scrape Viral AI Content", "type": "main", "index": 0}, {"node": "Scrape LinkedIn AI Posts", "type": "main", "index": 0}]]}, "Research AI Marketing Trends": {"main": [[{"node": "Merge All Research", "type": "main", "index": 0}]]}, "Scrape Viral AI Content": {"main": [[{"node": "Merge All Research", "type": "main", "index": 1}]]}, "Scrape LinkedIn AI Posts": {"main": [[{"node": "Merge All Research", "type": "main", "index": 2}]]}, "Merge All Research": {"main": [[{"node": "Ana<PERSON><PERSON> Viral Pattern<PERSON>", "type": "main", "index": 0}]]}, "Analyze Viral Patterns": {"main": [[{"node": "Generate Instagram Content", "type": "main", "index": 0}, {"node": "Generate LinkedIn Content", "type": "main", "index": 0}, {"node": "Generate Twitter Content", "type": "main", "index": 0}, {"node": "Generate Weekly AI News", "type": "main", "index": 0}]]}, "Generate Instagram Content": {"main": [[{"node": "Compile Content Package", "type": "main", "index": 0}]]}, "Generate LinkedIn Content": {"main": [[{"node": "Compile Content Package", "type": "main", "index": 1}]]}, "Generate Twitter Content": {"main": [[{"node": "Compile Content Package", "type": "main", "index": 2}]]}, "Generate Weekly AI News": {"main": [[{"node": "Compile Content Package", "type": "main", "index": 3}]]}, "Compile Content Package": {"main": [[{"node": "Save to Content Calendar", "type": "main", "index": 0}, {"node": "Send Content Summary", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}