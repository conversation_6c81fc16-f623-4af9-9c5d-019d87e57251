{"name": "Nikesh AI Lead Generation Assistant", "nodes": [{"id": "webhook-trigger", "name": "Instagram DM Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "parameters": {"path": "instagram-dm", "httpMethod": "POST", "responseMode": "responseNode"}}, {"id": "check-ai-keyword", "name": "Check for AI Keyword", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [460, 300], "parameters": {"conditions": {"options": {"caseSensitive": false}, "conditions": [{"id": "ai-keyword", "leftValue": "{{ $json.message }}", "rightValue": "AI", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}}}, {"id": "extract-user-info", "name": "Extract User Information", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [680, 240], "parameters": {"assignments": {"assignments": [{"id": "username", "name": "username", "value": "{{ $json.username || $json.from_username }}", "type": "string"}, {"id": "message", "name": "message", "value": "{{ $json.message || $json.text }}", "type": "string"}, {"id": "timestamp", "name": "timestamp", "value": "{{ $now }}", "type": "string"}, {"id": "source", "name": "source", "value": "Instagram DM", "type": "string"}]}}}, {"id": "research-prospect", "name": "Research Prospect Profile", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 240], "parameters": {"url": "https://api.apify.com/v2/acts/apify~instagram-profile-scraper/run-sync-get-dataset-items", "method": "POST", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer YOUR_APIFY_TOKEN"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonParameters": {"parameters": [{"name": "usernames", "value": "[\"{{ $json.username }}\"]"}]}}}, {"id": "qualify-lead", "name": "Qualify Lead with AI", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1120, 240], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are a lead qualification assistant for <PERSON><PERSON> G<PERSON>'s AI marketing services. Analyze prospect profiles to determine if they're a good fit for high-ticket AI marketing services ($2000+). Focus on company size, industry, role, and AI readiness."}, {"role": "user", "content": "Analyze this prospect who DMed 'AI' for a free audit:\n\nUsername: {{ $node['Extract User Information'].json.username }}\nProfile Data: {{ $json }}\n\nProvide:\n1. Lead Score (1-10)\n2. Qualification Status (Hot/Warm/Cold)\n3. Key indicators (company size, role, industry)\n4. Recommended next steps\n5. Personalized outreach angle\n6. Potential service fit (brand strategy, content creation, tool implementation)\n\nFocus on prospects who:\n- Are decision makers (<PERSON><PERSON><PERSON>, Marketing Head, Founder)\n- Work at companies with >50 employees or significant revenue\n- Are in fashion, retail, DTC, or tech industries\n- Show AI interest but need guidance\n- Are based in India, US, or Europe"}]}, "maxTokens": 500, "temperature": 0.3}}, {"id": "generate-response", "name": "Generate Personalized Response", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1340, 240], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are <PERSON><PERSON> responding to a DM inquiry. Write personalized responses that build trust, provide immediate value, and guide prospects toward a free audit call. Use his voice: business-casual, concrete, helpful without being pushy."}, {"role": "user", "content": "Lead Qualification: {{ $node['Qualify Lead with AI'].json.choices[0].message.content }}\n\nWrite a personalized DM response that:\n1. Thanks them for reaching out\n2. Provides one specific AI marketing insight relevant to their profile\n3. Offers the free audit with clear value proposition\n4. Includes Calendly link\n5. Keeps it under 150 words\n6. Feels personal, not automated\n\nTone: Professional but approachable, helpful, confident\nGoal: Get them to book the audit call"}]}, "maxTokens": 300, "temperature": 0.7}}, {"id": "save-lead-data", "name": "Save Lead to CRM", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.6, "position": [1560, 240], "parameters": {"operation": "append", "documentId": "YOUR_LEADS_SHEET_ID", "sheetName": "Leads", "range": "A:H", "options": {"valueInputOption": "USER_ENTERED"}, "columns": {"mappingMode": "defineBelow", "value": {"Username": "={{ $node['Extract User Information'].json.username }}", "Source": "={{ $node['Extract User Information'].json.source }}", "Message": "={{ $node['Extract User Information'].json.message }}", "Lead Score": "={{ $node['Qualify Lead with AI'].json.choices[0].message.content }}", "Response Sent": "={{ $node['Generate Personalized Response'].json.choices[0].message.content }}", "Timestamp": "={{ $node['Extract User Information'].json.timestamp }}", "Status": "New Lead", "Follow-up Date": "={{ $now.plus({days: 3}) }}"}}}}, {"id": "send-notification", "name": "Notify Nikesh", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1560, 380], "parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "🔥 New Qualified Lead: @{{ $node['Extract User Information'].json.username }}", "message": "New lead alert! 🚨\n\n👤 USERNAME: @{{ $node['Extract User Information'].json.username }}\n📱 SOURCE: {{ $node['Extract User Information'].json.source }}\n💬 MESSAGE: {{ $node['Extract User Information'].json.message }}\n⏰ TIME: {{ $node['Extract User Information'].json.timestamp }}\n\n🎯 QUALIFICATION:\n{{ $node['Qualify Lead with AI'].json.choices[0].message.content }}\n\n📝 SUGGESTED RESPONSE:\n{{ $node['Generate Personalized Response'].json.choices[0].message.content }}\n\n✅ Lead saved to CRM\n🔄 Follow-up scheduled for 3 days\n\nTime to close another high-ticket client! 💰", "options": {}}}, {"id": "webhook-response", "name": "Send Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 240], "parameters": {"options": {}, "responseBody": "{{ $node['Generate Personalized Response'].json.choices[0].message.content }}"}}, {"id": "cold-lead-response", "name": "Send Generic Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 380], "parameters": {"options": {}, "responseBody": "Thanks for your interest in AI marketing! 🚀 I'd love to help you explore how AI can transform your brand's growth. Check out my latest posts for actionable insights, and feel free to DM me with specific questions about your marketing challenges!"}}], "connections": {"Instagram DM Webhook": {"main": [[{"node": "Check for AI Keyword", "type": "main", "index": 0}]]}, "Check for AI Keyword": {"main": [[{"node": "Extract User Information", "type": "main", "index": 0}], [{"node": "Send Generic Response", "type": "main", "index": 0}]]}, "Extract User Information": {"main": [[{"node": "Research Prospect Profile", "type": "main", "index": 0}]]}, "Research Prospect Profile": {"main": [[{"node": "Qualify Lead with AI", "type": "main", "index": 0}]]}, "Qualify Lead with AI": {"main": [[{"node": "Generate Personalized Response", "type": "main", "index": 0}]]}, "Generate Personalized Response": {"main": [[{"node": "Save Lead to CRM", "type": "main", "index": 0}, {"node": "Notify Nikesh", "type": "main", "index": 0}, {"node": "Send Webhook Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}