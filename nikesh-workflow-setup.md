# Nikesh AI Marketing Content Workflow Setup Guide

## Overview
This specialized n8n workflow is designed specifically for Nikesh Ghosh's AI marketing content needs. It automates the research, creation, and formatting of content across Instagram, LinkedIn, and Twitter, focusing on AI marketing trends and tools that appeal to CXOs and brand leaders.

## What This Workflow Does

### 🔍 **Advanced Research Phase**
- **Perplexity AI Research**: Latest AI marketing trends, tools, and case studies with citations
- **Instagram Scraping**: Viral AI marketing content analysis via Apify
- **LinkedIn Scraping**: Professional AI marketing discussions and engagement patterns
- **Viral Pattern Analysis**: AI-powered analysis of what content performs best

### 🎯 **Platform-Specific Content Generation**
- **Instagram**: 30-45 second reel scripts with hooks, visual cues, captions, and hashtags
- **LinkedIn**: Professional posts with ROI focus and business value propositions
- **Twitter**: Punchy insights, threads, and quote-tweet ready content
- **Weekly AI News**: Curated roundup of important AI marketing developments

### 📊 **Organized Output**
- **Google Sheets**: Structured content calendar with all platforms
- **Email Summary**: Comprehensive weekly content package delivery
- **Brand Voice Consistency**: Maintains <PERSON><PERSON>'s business-casual, concrete tone

## Prerequisites

### Required API Keys & Accounts
1. **Perplexity API Key** - For advanced AI research with citations
2. **OpenAI API Key** - For content generation and analysis
3. **Apify API Token** - For social media scraping
4. **Google Account** - For Sheets integration
5. **Email Account** - For content delivery

### Recommended Setup
- **Apify Subscription**: For higher scraping limits
- **Perplexity Pro**: For better research quality
- **Google Workspace**: For team collaboration on content calendar

## Step-by-Step Setup

### Step 1: Import the Workflow
1. Copy content from `nikesh-ai-content-workflow.json`
2. In n8n: **Workflows** → **Import from JSON**
3. Paste and click **Import**

### Step 2: Configure API Credentials

#### Perplexity API Setup
1. Sign up at [perplexity.ai](https://perplexity.ai)
2. Get API key from dashboard
3. In n8n, click **Perplexity** nodes → **Create New Credential**
4. Enter API key and test connection

#### Apify Configuration
1. Create account at [apify.com](https://apify.com)
2. Get API token from console
3. In **HTTP Request** nodes for Apify:
   - Replace `YOUR_APIFY_TOKEN` with actual token
   - Test with small scraping limits first

#### OpenAI Setup
1. Configure OpenAI credentials in all OpenAI nodes
2. Recommended models: GPT-4 for better content quality
3. Monitor usage to manage costs

### Step 3: Configure Google Sheets
1. Create new Google Sheet with these columns:
   - Username | Content Focus | Content Pillar | Instagram Content | LinkedIn Content | Twitter Content | Weekly AI News | Viral Insights | Created Date | Status
2. Name the sheet tab "Content Calendar"
3. Get Sheet ID from URL and replace `YOUR_GOOGLE_SHEET_ID`
4. Configure Google Sheets credentials

### Step 4: Set Up Email Notifications
1. Configure SMTP settings in **Send Content Summary** node
2. Update email addresses:
   - From: `<EMAIL>`
   - To: `<EMAIL>`
3. Test email delivery

## Content Pillars & Focus Areas

### Default Content Pillars (Customizable)
1. **Founder's Lens** - Leadership insights and journey
2. **AI for Creatives & Brands** - Tool reviews and tutorials
3. **BTS & Case Studies** - Offbeat Origins success stories
4. **Reality Check** - Balanced AI perspectives and ethics
5. **News Round Up** - Weekly AI marketing developments

### Content Focus Examples
- "Latest AI marketing tools for DTC brands"
- "AI-powered creative workflows for fashion brands"
- "Cost-effective AI solutions for small businesses"
- "AI marketing ROI case studies"
- "Emerging AI trends in Indian market"

## How to Use

### Basic Weekly Execution
1. Click **Execute Workflow** on Manual Trigger
2. Workflow runs with default parameters (takes 5-10 minutes)
3. Receive email summary with all content
4. Review content in Google Sheets
5. Schedule posts across platforms

### Custom Content Creation
Modify the **Set Content Parameters** node with:
```json
{
  "content_focus": "AI tools for fashion brand marketing",
  "content_type": "educational_reel",
  "target_audience": "Fashion brand CMOs in India",
  "content_pillar": "AI for Creatives & Brands"
}
```

### Weekly Schedule Recommendation
- **Monday**: Run workflow for week's content
- **Tuesday**: Review and refine content
- **Wednesday-Friday**: Post content across platforms
- **Weekend**: Engage with audience and gather feedback

## Expected Output Format

### Instagram Content
```
REEL SCRIPT (30-45 seconds):
Hook: "3 AI tools that saved me 20 hours this week..."
Point 1: Tool name + specific benefit
Point 2: Real ROI example
Point 3: Implementation tip
CTA: "DM 'AI' for free audit"

VISUAL CUES:
- Screen recordings of tools
- Before/after comparisons
- Text overlays with statistics

CAPTION:
Engaging story + insights + hashtags
#AIMarketing #MarketingAI #BrandingAI
```

### LinkedIn Content
```
Professional post with:
- Business problem statement
- 3-4 actionable insights
- Specific ROI data
- Professional CTA
- 3-5 relevant hashtags
```

### Twitter Content
```
Single Tweet: Quick insight under 280 chars
Thread: 5-7 tweets with examples
Quote Tweet: Commentary on trends
```

## Customization Options

### Adding More Research Sources
- **YouTube API**: Trending AI marketing videos
- **Reddit API**: Community discussions
- **News APIs**: Industry publications
- **Company APIs**: Tool-specific data

### Content Variations
- **Video Scripts**: Longer YouTube content
- **Blog Outlines**: Detailed articles
- **Email Newsletters**: Subscriber content
- **Webinar Topics**: Educational sessions

### Advanced Features
- **Competitor Analysis**: Track competitor content
- **Trend Prediction**: AI-powered forecasting
- **Performance Tracking**: Engagement analysis
- **A/B Testing**: Multiple content versions

## Cost Management

### Expected Monthly Costs
- **Perplexity API**: $20-50/month
- **OpenAI API**: $30-80/month (depending on usage)
- **Apify**: $49/month (starter plan)
- **Total**: ~$100-180/month

### Cost Optimization Tips
1. Use Perplexity "sonar" model for basic research
2. Batch content creation (weekly vs daily)
3. Monitor OpenAI token usage
4. Use Apify free tier for testing

## Success Metrics to Track

### Content Performance
- Engagement rates by platform
- DM inquiries with "AI" keyword
- Profile visits and follows
- Content saves and shares

### Business Impact
- Inbound leads generated
- Audit requests received
- Client inquiries from content
- Thought leadership mentions

### Efficiency Gains
- Time saved on research (target: 4-5 hours daily)
- Content creation speed
- Consistency of posting
- Quality of generated leads

## Troubleshooting

### Common Issues
1. **Apify Rate Limits**: Reduce scraping frequency
2. **Perplexity Timeouts**: Use shorter queries
3. **OpenAI Costs**: Monitor token usage
4. **Content Quality**: Refine prompts iteratively

### Quality Control
- Review generated content before posting
- Maintain brand voice consistency
- Fact-check AI-generated statistics
- Ensure compliance with platform guidelines

## Next Steps

1. **Week 1**: Test workflow with default settings
2. **Week 2**: Customize for specific content pillars
3. **Week 3**: Add performance tracking
4. **Week 4**: Optimize based on engagement data

---

**Ready to automate your path to AI marketing thought leadership! 🚀**
