{"name": "AI Content Research Agent", "nodes": [{"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {"temperature": 0.7, "maxTokens": 4000}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-272, 384], "id": "4f85ec74-5f14-45c2-a8ae-184334b9fab5", "name": "GPT-4o Research Model", "credentials": {"openAiApi": {"id": "r06G59KYGU7WhU6u", "name": "OpenAi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-272, 16], "id": "1a6636ee-3b67-45bc-8984-5517e31a9999", "name": "When chat message received", "webhookId": "8cdb1524-c833-4ccf-b518-4b6d139113ba"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-96, 400], "id": "a9530601-e1ba-432e-a2d2-0cf970172c13", "name": "Simple Memory"}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"tools\",\"name of the tool\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [512, 336], "id": "d4d22251-104f-4ee4-b38e-5f1df17dca3f", "name": "Web Research Tool", "credentials": {"mcpClientApi": {"id": "VZrolHwLFQcQ8BY1", "name": "exa "}}}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"Tools\",\"name of the tools\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [64, 400], "id": "290581d9-924c-4e9f-b4b1-a351c9aa5ba8", "name": "Web Scraping Tool", "credentials": {"mcpClientApi": {"id": "R1W9MSewn63K3ugI", "name": "playwright"}}}, {"parameters": {"promptType": "define", "text": "You are an Intelligent Research Agent that analyzes user queries and selects the most appropriate research tools. NEVER ask for clarification - immediately analyze the query and begin research.\n\n## QUERY ANALYSIS & TOOL SELECTION\n\nAnalyze the user's query and choose tools intelligently:\n\n**For General Topics/Trends:** Use web_search_exa_exa\n- \"AI tools\", \"marketing trends\", \"productivity apps\"\n- Search: \"[topic] trending 2024\", \"[topic] viral content\", \"[topic] popular posts\"\n\n**For Platform-Specific Research:** Use appropriate tools\n- \"LinkedIn posts about X\" → linkedin_search_exa_exa\n- \"Instagram reels about X\" → web_search_exa_exa + crawling_exa_exa\n- \"Twitter threads about X\" → web_search_exa_exa\n- \"Reddit discussions about X\" → web_search_exa_exa\n\n**For Company/Brand Research:** Use company_research_exa_exa\n- \"[Company name] content strategy\", \"competitors in [industry]\"\n\n**For Specific URLs/Websites:** Use crawling_exa_exa\n- \"Analyze this post: [URL]\", \"Research this website\"\n\n**For Deep Analysis:** Use multiple tools in sequence\n- Start with web_search_exa_exa for overview\n- Use crawling_exa_exa for specific content\n- Use company_research_exa_exa for business context\n\n## RESEARCH EXECUTION\n\n1. **Analyze Query Type** - Determine what kind of research is needed\n2. **Select Appropriate Tools** - Choose 2-4 tools based on query\n3. **Execute Research** - Use tools with targeted search queries\n4. **Synthesize Findings** - Combine data from all sources\n\n## OUTPUT FORMAT\n\n🔍 **RESEARCH METHODOLOGY**\n- Query analysis and tool selection reasoning\n- Research approach taken\n- Data sources utilized\n\n� **KEY FINDINGS**\n- 5-7 major trends discovered\n- Specific examples with metrics\n- Pain points and opportunities identified\n- Competitive landscape insights\n\n🎯 **CONTENT OPPORTUNITIES**\n- High-potential content angles\n- Viral content patterns identified\n- Audience engagement insights\n- Platform-specific optimization notes\n\n� **ACTIONABLE DATA**\n- Performance metrics found\n- Engagement patterns\n- Optimal posting strategies\n- Content format recommendations\n\nALWAYS use multiple tools when appropriate. NEVER limit yourself to just web search - be intelligent about tool selection based on the specific query provided.", "options": {"systemMessage": "You are an expert AI Content Research Specialist. Conduct comprehensive multi-platform research to generate viral content ideas and actionable insights for content creators."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [0, 0], "id": "2894cf0a-fbc3-488b-bdec-d3169443df9d", "name": "Content Research Specialist"}, {"parameters": {"operation": "executeTool", "toolName": "={{ $fromAI(\"tools\",\"name of the tool\") }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [304, 384], "id": "4404442e-046f-4a0c-a5d6-0d8430d73d2d", "name": "Social Media Research Tool", "credentials": {"mcpClientApi": {"id": "S4jTEIdHfBSdiiin", "name": "MCP Client (STDIO) account 2"}}}], "pinData": {}, "connections": {"GPT-4o Research Model": {"ai_languageModel": [[{"node": "Content Research Specialist", "type": "ai_languageModel", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Content Research Specialist", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "Content Research Specialist", "type": "ai_memory", "index": 0}]]}, "Web Research Tool": {"ai_tool": [[{"node": "Content Research Specialist", "type": "ai_tool", "index": 0}]]}, "Web Scraping Tool": {"ai_tool": [[{"node": "Content Research Specialist", "type": "ai_tool", "index": 0}]]}, "Social Media Research Tool": {"ai_tool": [[{"node": "Content Research Specialist", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5b208b76-95d3-403a-b929-6140c61e4d0f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "733f60c7b0c4609e60ec0cf958e7904eebc14a5810e0081aca3a300d7426df10"}, "id": "C9sCy896qoeZK3tX", "tags": []}