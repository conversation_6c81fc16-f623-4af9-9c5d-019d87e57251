{"name": "Simple Content Creator Assistant", "nodes": [{"id": "manual-trigger", "name": "Start Research", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300], "parameters": {}}, {"id": "set-topic", "name": "Set Topic", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 300], "parameters": {"assignments": {"assignments": [{"id": "topic", "name": "topic", "value": "{{ $json.topic || 'artificial intelligence trends 2024' }}", "type": "string"}, {"id": "content_type", "name": "content_type", "value": "{{ $json.content_type || 'YouTube video' }}", "type": "string"}]}}}, {"id": "reddit-search", "name": "Reddit Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, 300], "parameters": {"url": "https://www.reddit.com/search.json?q={{ $json.topic }}&sort=hot&limit=5", "method": "GET", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "n8n-research-bot/1.0"}]}}}, {"id": "ai-research-summary", "name": "Create Research Summary", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [900, 300], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are a content research assistant. Create a concise research summary and content outline based on the provided Reddit data."}, {"role": "user", "content": "Topic: {{ $node['Set Topic'].json.topic }}\nContent Type: {{ $node['Set Topic'].json.content_type }}\n\nReddit Research Data: {{ $json }}\n\nPlease provide:\n1. Key insights from the research\n2. 3 main content angles\n3. Basic script outline with intro, main points, and conclusion\n4. Potential hooks for engagement\n\nKeep it concise and actionable."}]}, "maxTokens": 800, "temperature": 0.7}}, {"id": "generate-script", "name": "Generate Script", "type": "n8n-nodes-base.openAi", "typeVersion": 1.1, "position": [1120, 300], "parameters": {"resource": "chat", "operation": "complete", "prompt": {"messages": [{"role": "system", "content": "You are a script writer. Create an engaging script based on the research summary provided."}, {"role": "user", "content": "Research Summary: {{ $node['Create Research Summary'].json.choices[0].message.content }}\n\nCreate a detailed script for a {{ $node['Set Topic'].json.content_type }} about {{ $node['Set Topic'].json.topic }}.\n\nInclude:\n1. Attention-grabbing opening (15 seconds)\n2. Main content sections with smooth transitions\n3. Engaging conclusion with call-to-action\n4. Approximate timing for each section\n\nMake it conversational and engaging."}]}, "maxTokens": 1000, "temperature": 0.8}}, {"id": "format-output", "name": "Format Final Output", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1340, 300], "parameters": {"assignments": {"assignments": [{"id": "topic", "name": "topic", "value": "{{ $node['Set Topic'].json.topic }}", "type": "string"}, {"id": "content_type", "name": "content_type", "value": "{{ $node['Set Topic'].json.content_type }}", "type": "string"}, {"id": "research_summary", "name": "research_summary", "value": "{{ $node['Create Research Summary'].json.choices[0].message.content }}", "type": "string"}, {"id": "script", "name": "script", "value": "{{ $node['Generate Script'].json.choices[0].message.content }}", "type": "string"}, {"id": "created_at", "name": "created_at", "value": "{{ $now }}", "type": "string"}]}}}, {"id": "send-results", "name": "Send Results Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [1560, 300], "parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>", "subject": "Content Ready: {{ $json.topic }}", "message": "Your content research and script for '{{ $json.topic }}' is ready!\n\n📝 CONTENT TYPE: {{ $json.content_type }}\n⏰ CREATED: {{ $json.created_at }}\n\n🔍 RESEARCH SUMMARY:\n{{ $json.research_summary }}\n\n📜 SCRIPT:\n{{ $json.script }}\n\n---\nGenerated by n8n Content Creator Assistant", "options": {}}}], "connections": {"Start Research": {"main": [[{"node": "Set Topic", "type": "main", "index": 0}]]}, "Set Topic": {"main": [[{"node": "Reddit Research", "type": "main", "index": 0}]]}, "Reddit Research": {"main": [[{"node": "Create Research Summary", "type": "main", "index": 0}]]}, "Create Research Summary": {"main": [[{"node": "Generate Script", "type": "main", "index": 0}]]}, "Generate Script": {"main": [[{"node": "Format Final Output", "type": "main", "index": 0}]]}, "Format Final Output": {"main": [[{"node": "Send Results Email", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}