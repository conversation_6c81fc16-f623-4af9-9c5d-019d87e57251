# Content Creator Research & Scripting Workflow Setup Guide

## Overview
This n8n workflow automates the research and scripting process for content creators, saving hours of manual work. It gathers data from multiple sources, processes it with AI, and generates comprehensive content packages including research summaries, script outlines, content ideas, and fact-checking notes.

## What This Workflow Does

### 🔍 **Research Phase**
- **Reddit Research**: Finds trending discussions and community insights
- **News Research**: Gathers latest news articles and current events
- **Google Trends**: Identifies trending topics and search patterns

### 🤖 **AI Processing Phase**
- **Research Analysis**: Summarizes and analyzes all gathered data
- **Script Generation**: Creates detailed script outlines with timing and structure
- **Idea Generation**: Produces 10 unique content ideas with different angles
- **Fact Checking**: Verifies information and flags potential issues

### 📊 **Output Phase**
- **Google Sheets**: Saves all data in an organized spreadsheet
- **Email Summary**: Sends a comprehensive report to your inbox

## Prerequisites

### Required API Keys
1. **OpenAI API Key** - For AI processing (required)
2. **News API Key** - For news research (optional but recommended)
3. **SerpAPI Key** - For Google Trends data (optional but recommended)

### Required Accounts
1. **Google Account** - For Google Sheets integration
2. **Email Account** - For receiving summaries

## Setup Instructions

### Step 1: Import the Workflow
1. Copy the content from `content-creator-workflow.json`
2. In n8n, go to **Workflows** → **Import from JSON**
3. Paste the JSON content and click **Import**

### Step 2: Configure API Keys

#### OpenAI Configuration
1. Click on any **OpenAI** node
2. Click **Create New Credential**
3. Enter your OpenAI API key
4. Test the connection

#### News API Configuration (Optional)
1. Sign up at [newsapi.org](https://newsapi.org)
2. Get your free API key
3. In the **News Research** node, replace `YOUR_NEWS_API_KEY` with your actual key

#### SerpAPI Configuration (Optional)
1. Sign up at [serpapi.com](https://serpapi.com)
2. Get your API key
3. In the **Google Trends Research** node, replace `YOUR_SERPAPI_KEY` with your actual key

### Step 3: Configure Google Sheets
1. Click on the **Save to Google Sheets** node
2. Create a new Google Sheets credential or use existing
3. Create a new spreadsheet or use existing
4. Replace `YOUR_GOOGLE_SHEET_ID` with your actual sheet ID
5. Ensure the sheet has a tab named "Content Research"

### Step 4: Configure Email
1. Click on the **Send Email Summary** node
2. Configure your SMTP settings or use a service like Gmail
3. Update the `fromEmail` and `toEmail` addresses

## How to Use

### Basic Usage
1. Click the **Execute Workflow** button on the Manual Trigger
2. The workflow will run with default settings:
   - Topic: "artificial intelligence"
   - Content Type: "youtube video"
   - Target Audience: "general audience"
   - Research Depth: "medium"

### Custom Input
To provide custom inputs, you can modify the **Set Research Variables** node or use a webhook trigger with form data.

#### Example Input JSON:
```json
{
  "topic": "sustainable energy",
  "content_type": "blog post",
  "target_audience": "environmentally conscious consumers",
  "research_depth": "deep"
}
```

## Workflow Outputs

### Google Sheets Columns
- **Topic**: The research topic
- **Content Type**: Type of content being created
- **Target Audience**: Intended audience
- **Research Summary**: AI-processed research insights
- **Script Outline**: Detailed script structure
- **Content Ideas**: 10 unique content ideas
- **Fact Check**: Verification notes and source recommendations
- **Created Date**: Timestamp of workflow execution

### Email Summary
Comprehensive email containing all research and generated content, formatted for easy reading.

## Customization Options

### Adding More Research Sources
You can add additional HTTP Request nodes for:
- Twitter/X API
- YouTube API
- Industry-specific APIs
- Academic databases

### Modifying AI Prompts
Each OpenAI node can be customized:
- Adjust temperature for creativity level
- Modify max tokens for response length
- Update system prompts for different styles
- Add specific instructions for your niche

### Alternative Output Options
Replace or add to the output nodes:
- **Notion**: Save to Notion database
- **Slack**: Send to team channels
- **Airtable**: Organize in project management tool
- **Discord**: Share with community

## Troubleshooting

### Common Issues
1. **API Rate Limits**: Add Wait nodes between API calls
2. **Large Responses**: Increase max tokens or add text splitting
3. **Failed Connections**: Check API keys and network connectivity
4. **Missing Data**: Verify node connections and data flow

### Error Handling
Consider adding:
- Error Trigger nodes for failed executions
- Conditional logic for missing API responses
- Retry mechanisms for network failures

## Cost Considerations

### OpenAI Usage
- Estimated cost: $0.10-0.50 per workflow execution
- Depends on topic complexity and response length
- Monitor usage in OpenAI dashboard

### API Limits
- News API: 1000 requests/month (free tier)
- SerpAPI: 100 searches/month (free tier)
- Reddit: No API key required, but respect rate limits

## Next Steps

1. **Test the workflow** with a simple topic first
2. **Customize prompts** for your specific niche
3. **Add error handling** for production use
4. **Create variations** for different content types
5. **Schedule regular runs** for trending topic research

## Support

For issues or questions:
1. Check n8n documentation
2. Review API provider documentation
3. Test individual nodes in isolation
4. Monitor execution logs for errors

---

**Happy Content Creating! 🎬✨**
